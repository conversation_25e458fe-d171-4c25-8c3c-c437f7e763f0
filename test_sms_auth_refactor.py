#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SMS 认证重构验证测试
"""

import asyncio
from app.services.sms_auth_service import sms_auth_service, AuthenticationResult


async def test_sms_auth_service():
    """测试 SMS 认证服务基本功能"""
    print("开始测试 SMS 认证服务...")
    
    # 测试服务实例化
    assert sms_auth_service is not None
    print("✓ SMS 认证服务实例化成功")
    
    # 测试认证结果类
    result = AuthenticationResult(
        success=True,
        access_token="test_token",
        message="测试成功"
    )
    assert result.success is True
    assert result.access_token == "test_token"
    assert result.message == "测试成功"
    print("✓ AuthenticationResult 类工作正常")
    
    # 测试手机号验证（模拟）
    try:
        from app.services.validate_phone_number import validate_phone_number
        validated = validate_phone_number("13800138000")
        print(f"✓ 手机号验证功能正常: {validated}")
    except Exception as e:
        print(f"⚠ 手机号验证功能测试跳过: {e}")
    
    print("SMS 认证服务重构验证完成！")


if __name__ == "__main__":
    asyncio.run(test_sms_auth_service())