#!/usr/bin/env python3
"""测试异步 Redis 操作"""

import asyncio
import json
from datetime import datetime

from app.services.recommendation_cache_service import recommendation_cache_service
from app.schemas.recommendation import RecommendationItem, RecommendationResponse


async def test_async_redis_operations():
    """测试异步 Redis 操作"""
    print("开始测试异步 Redis 操作...")
    
    # 测试数据
    user_id = 12345
    algorithm_type = "collaborative_filtering"
    
    # 创建测试推荐数据
    test_items = [
        RecommendationItem(
            content_type="article",
            content_id=1,
            score=0.95,
            reason="基于用户兴趣"
        ),
        RecommendationItem(
            content_type="video",
            content_id=2,
            score=0.88,
            reason="热门内容"
        )
    ]
    
    test_recommendations = RecommendationResponse(
        items=test_items,
        algorithm_type=algorithm_type,
        total_count=2,
        generated_at=datetime.now()
    )
    
    try:
        # 测试设置用户推荐缓存
        print("测试设置用户推荐缓存...")
        result = await recommendation_cache_service.set_user_recommendations(
            user_id=user_id,
            algorithm_type=algorithm_type,
            recommendations=test_recommendations
        )
        print(f"设置用户推荐缓存结果: {result}")
        
        # 测试获取用户推荐缓存
        print("测试获取用户推荐缓存...")
        cached_recommendations = await recommendation_cache_service.get_user_recommendations(
            user_id=user_id,
            algorithm_type=algorithm_type
        )
        print(f"获取用户推荐缓存结果: {cached_recommendations is not None}")
        if cached_recommendations:
            print(f"缓存的推荐数量: {len(cached_recommendations.items)}")
        
        # 测试设置热门内容缓存
        print("测试设置热门内容缓存...")
        hot_content = [
            {"content_id": 1, "content_type": "article", "score": 0.95},
            {"content_id": 2, "content_type": "video", "score": 0.88}
        ]
        result = await recommendation_cache_service.set_hot_content(
            content_type="article",
            hot_content=hot_content
        )
        print(f"设置热门内容缓存结果: {result}")
        
        # 测试获取热门内容缓存
        print("测试获取热门内容缓存...")
        cached_hot_content = await recommendation_cache_service.get_hot_content(
            content_type="article"
        )
        print(f"获取热门内容缓存结果: {cached_hot_content is not None}")
        if cached_hot_content:
            print(f"缓存的热门内容数量: {len(cached_hot_content)}")
        
        # 测试设置用户画像缓存
        print("测试设置用户画像缓存...")
        profile_data = {
            "interests": ["technology", "sports"],
            "age_group": "25-35",
            "location": "Beijing"
        }
        result = await recommendation_cache_service.set_user_profile_cache(
            user_id=user_id,
            profile_data=profile_data
        )
        print(f"设置用户画像缓存结果: {result}")
        
        # 测试获取用户画像缓存
        print("测试获取用户画像缓存...")
        cached_profile = await recommendation_cache_service.get_user_profile_cache(
            user_id=user_id
        )
        print(f"获取用户画像缓存结果: {cached_profile is not None}")
        if cached_profile:
            print(f"缓存的用户画像: {cached_profile}")
        
        # 测试清除用户缓存
        print("测试清除用户缓存...")
        result = await recommendation_cache_service.invalidate_user_cache(user_id=user_id)
        print(f"清除用户缓存结果: {result}")
        
        print("所有异步 Redis 操作测试完成！")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_async_redis_operations())
