import asyncio
import httpx

async def test_read_video_with_stats():
    """测试获取带统计信息的视频详情"""
    # 假设有一个视频 ID 为 1
    video_id = 1
    async with httpx.AsyncClient(base_url="http://localhost:8000") as client:
        response = await client.get(f"/api/v1/videos/{video_id}/with-stats")
        print(f"Status code: {response.status_code}")
        if response.status_code == 200:
            print("Response JSON:")
            print(response.json())
        else:
            print("Error response:")
            print(response.text)

async def test_read_article_with_stats():
    """测试获取带统计信息的文章详情"""
    # 假设有一个文章 ID 为 1
    article_id = 1
    async with httpx.AsyncClient(base_url="http://localhost:8000") as client:
        response = await client.get(f"/api/v1/articles/{article_id}/with-stats")
        print(f"Status code: {response.status_code}")
        if response.status_code == 200:
            print("Response JSON:")
            print(response.json())
        else:
            print("Error response:")
            print(response.text)

asyncio.run(test_read_video_with_stats())
asyncio.run(test_read_article_with_stats())