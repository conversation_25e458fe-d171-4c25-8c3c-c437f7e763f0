#!/usr/bin/env python3
"""
测试统一登录注册功能的脚本
"""

import asyncio
import httpx
import json

# 测试配置
BASE_URL = "http://localhost:8000"
TEST_PHONE = "13800138000"  # 测试手机号


async def test_unified_auth():
    """测试统一的登录注册流程"""
    async with httpx.AsyncClient() as client:
        print("=== 测试统一登录注册功能 ===\n")
        
        # 1. 发送验证码
        print("1. 发送验证码...")
        send_response = await client.post(
            f"{BASE_URL}/api/v1/auth/send-auth-code",
            json={"phone": TEST_PHONE}
        )
        
        print(f"状态码: {send_response.status_code}")
        if send_response.status_code == 200:
            data = send_response.json()
            print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            is_registered = data.get("is_registered", False)
            action = data.get("action", "unknown")
            
            print(f"用户状态: {'已注册' if is_registered else '未注册'}")
            print(f"操作类型: {action}")
            
            # 2. 模拟验证码验证（在开发环境下，验证码会在日志中显示）
            print("\n2. 请查看服务器日志获取验证码，然后输入验证码进行验证...")
            verification_code = input("请输入验证码: ")
            
            # 3. 验证验证码
            print("\n3. 验证验证码...")
            verify_response = await client.post(
                f"{BASE_URL}/api/v1/auth/verify-auth-code",
                json={
                    "phone": TEST_PHONE,
                    "code": verification_code
                }
            )
            
            print(f"状态码: {verify_response.status_code}")
            if verify_response.status_code == 200:
                data = verify_response.json()
                print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                
                if "access_token" in data:
                    print(f"\n✅ 认证成功！")
                    print(f"访问令牌: {data['access_token'][:50]}...")
                    if "user" in data:
                        user = data["user"]
                        print(f"用户信息:")
                        print(f"  - ID: {user.get('id')}")
                        print(f"  - 用户名: {user.get('username')}")
                        print(f"  - 昵称: {user.get('nickname')}")
                        print(f"  - 登录类型: {user.get('login_type')}")
                        
                        if not is_registered:
                            print(f"\n🎉 新用户自动注册成功！")
                        else:
                            print(f"\n🔑 用户登录成功！")
                            
                elif "requires_device_verification" in data:
                    print(f"\n🔐 需要设备验证")
                    print(f"验证令牌: {data.get('verification_token', '')[:50]}...")
                    device_info = data.get('device_info', {})
                    print(f"设备信息:")
                    print(f"  - 设备名称: {device_info.get('device_name')}")
                    print(f"  - 设备类型: {device_info.get('device_type')}")
                    print(f"  - 位置: {device_info.get('location')}")
                    print(f"  - 是否新设备: {device_info.get('is_new_device')}")
            else:
                print(f"❌ 验证失败: {verify_response.text}")
        else:
            print(f"❌ 发送验证码失败: {send_response.text}")


async def test_compatibility():
    """测试兼容性接口"""
    async with httpx.AsyncClient() as client:
        print("\n=== 测试兼容性接口 ===\n")
        
        # 测试原有的登录接口
        print("1. 测试原有登录接口...")
        login_response = await client.post(
            f"{BASE_URL}/api/v1/auth/send-login-code",
            json={"phone": TEST_PHONE}
        )
        
        print(f"登录验证码发送状态码: {login_response.status_code}")
        if login_response.status_code == 200:
            print(f"✅ 原有登录接口正常工作")
        else:
            print(f"❌ 原有登录接口异常: {login_response.text}")
        
        # 测试原有的注册接口
        print("\n2. 测试原有注册接口...")
        register_response = await client.post(
            f"{BASE_URL}/api/v1/auth/send-register-code",
            json={"phone": "13900139000"}  # 使用不同的手机号
        )
        
        print(f"注册验证码发送状态码: {register_response.status_code}")
        if register_response.status_code == 200:
            print(f"✅ 原有注册接口正常工作")
        else:
            print(f"❌ 原有注册接口异常: {register_response.text}")


async def main():
    """主函数"""
    try:
        await test_unified_auth()
        await test_compatibility()
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")


if __name__ == "__main__":
    print("开始测试统一登录注册功能...")
    print("请确保服务器正在运行在 http://localhost:8000")
    print("=" * 50)
    
    asyncio.run(main())
