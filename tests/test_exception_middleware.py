#!/usr/bin/env python3
"""
异常处理中间件测试脚本

这个脚本用于测试新的异常处理中间件是否正确处理HTTPException和其他异常。
"""

import asyncio
import httpx
import json


async def test_exception_handling():
    """测试异常处理中间件"""
    base_url = "http://localhost:8001"
    
    async with httpx.AsyncClient() as client:
        print("🧪 测试异常处理中间件...")
        
        # 测试1: 正常请求
        print("\n1. 测试正常请求...")
        try:
            response = await client.get(f"{base_url}/health")
            print(f"   状态码: {response.status_code}")
            print(f"   响应: {response.json()}")
        except Exception as e:
            print(f"   错误: {e}")
        
        # 测试2: 400错误（验证码错误）
        print("\n2. 测试400错误（验证码错误）...")
        try:
            response = await client.post(
                f"{base_url}/api/v1/auth/verify-auth-code",
                json={"phone": "13800138000", "code": "000000"}
            )
            print(f"   状态码: {response.status_code}")
            print(f"   响应: {response.json()}")
        except Exception as e:
            print(f"   错误: {e}")
        
        # 测试3: 422错误（参数验证错误）
        print("\n3. 测试422错误（参数验证错误）...")
        try:
            response = await client.post(
                f"{base_url}/api/v1/auth/verify-auth-code",
                json={"phone": "invalid_phone"}  # 缺少code字段
            )
            print(f"   状态码: {response.status_code}")
            print(f"   响应: {response.json()}")
        except Exception as e:
            print(f"   错误: {e}")
        
        # 测试4: 404错误
        print("\n4. 测试404错误...")
        try:
            response = await client.get(f"{base_url}/api/v1/nonexistent")
            print(f"   状态码: {response.status_code}")
            print(f"   响应: {response.json()}")
        except Exception as e:
            print(f"   错误: {e}")


if __name__ == "__main__":
    print("🚀 启动异常处理中间件测试")
    print("请确保服务器正在运行在 http://localhost:8000")
    
    try:
        asyncio.run(test_exception_handling())
        print("\n✅ 测试完成")
    except KeyboardInterrupt:
        print("\n❌ 测试被中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")