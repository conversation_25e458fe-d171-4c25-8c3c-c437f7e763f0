#!/usr/bin/env python

import asyncio
import sys
from sqlalchemy import select
from app.db.session import SessionLocal
from app.models.video_folder import VideoFolder
from app.models.video import Video
from app.services.folder_stats_service import folder_stats_service

async def test_video_folder_count():
    """测试视频文件夹计数功能"""
    print("开始测试视频文件夹计数功能...")
    
    async with SessionLocal() as session:
        # 获取一个视频文件夹
        result = await session.execute(select(VideoFolder).limit(1))
        folder = result.scalars().first()
        
        if not folder:
            print("未找到视频文件夹，创建一个测试文件夹")
            folder = VideoFolder(name="测试文件夹", user_id=1)
            session.add(folder)
            await session.commit()
            await session.refresh(folder)
        
        # 打印当前视频数量
        print(f"文件夹ID: {folder.id}, 名称: {folder.name}, 当前视频数量: {folder.video_count}")
        
        # 创建一个新视频
        video = Video(
            title="测试视频",
            description="测试视频描述",
            url="http://example.com/test.mp4",
            cover_url="http://example.com/test.jpg",
            author_id=1,
            folder_id=folder.id
        )
        session.add(video)
        await session.commit()
        await session.refresh(video)
        
        # 更新文件夹视频计数
        await folder_stats_service.update_folder_video_count(session, folder.id)
        await session.refresh(folder)
        
        # 打印更新后的视频数量
        print(f"添加视频后，文件夹视频数量: {folder.video_count}")
        
        # 删除测试视频
        await session.delete(video)
        await session.commit()
        
        # 更新文件夹视频计数
        await folder_stats_service.update_folder_video_count(session, folder.id)
        await session.refresh(folder)
        
        # 打印删除后的视频数量
        print(f"删除视频后，文件夹视频数量: {folder.video_count}")
        
        print("测试完成")

if __name__ == "__main__":
    asyncio.run(test_video_folder_count())