#!/usr/bin/env python3
"""测试微信认证功能"""

import pytest
import asyncio
import httpx
from httpx import AsyncClient
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession
from unittest.mock import AsyncMock, patch
from app.main import app
from app.api import deps
from app.models.user import User
from app.services.sms_verification import SmsVerification
from app.core.wechat_exceptions import WeChatErrors
import json
from datetime import datetime

# 测试配置
BASE_URL = "http://localhost:8000"
TEST_OPENID = "test_openid_123456"
TEST_USERNAME = "13800138000"  # 测试手机号
TEST_CODE = "000000"  # 开发模式万能验证码


class TestWeChatAuth:
    """微信认证功能测试类"""
    
    def setup_method(self):
        """测试前置设置"""
        self.client = TestClient(app)
        self.test_openid = "test_openid_123456"
        self.test_username = "13800138000"
        self.test_code = "123456"
        self.base_url = "/api/v1/wechat"
        
    @pytest.fixture
    def mock_sms_verification(self):
        """模拟短信验证服务"""
        with patch.object(SmsVerification, 'verify_code', return_value=True):
            yield
    
    @pytest.fixture
    def mock_wechat_service(self):
        """模拟微信服务"""
        mock_user_info = {
            'nickname': '测试用户',
            'headimgurl': 'https://example.com/avatar.jpg'
        }
        with patch('app.api.endpoints.wechat_auth.WeChatAuthService.get_wechat_user_info', return_value=mock_user_info):
            yield

    def test_new_user_registration(self, mock_sms_verification, mock_wechat_service):
        """测试新用户注册场景"""
        with patch('app.api.endpoints.wechat_auth.WeChatAuthService.validate_bind_request') as mock_validate, \
             patch('app.api.endpoints.wechat_auth.WeChatAuthService.check_existing_users') as mock_check, \
             patch('app.api.endpoints.wechat_auth.WeChatAuthService.process_bind_logic') as mock_process:
            
            # 模拟返回值
            mock_validate.return_value = None
            mock_check.return_value = (None, None)  # 用户不存在
            mock_user = User(id=1, username=self.test_username, wechat_openid=self.test_openid)
            mock_process.return_value = (mock_user, "用户创建并绑定微信成功")
            
            response = self.client.post(
                f"{self.base_url}/bind",
                json={
                    "openid": self.test_openid,
                    "username": self.test_username,
                    "code": self.test_code
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "message" in data
            assert "user" in data
            assert "access_token" in data
            assert data["user"]["username"] == self.test_username
            print(f"✅ 新用户注册测试通过: {data['message']}")

    def test_existing_user_bind(self, mock_sms_verification, mock_wechat_service):
        """测试现有用户绑定微信场景"""
        with patch('app.api.endpoints.wechat_auth.WeChatAuthService.validate_bind_request') as mock_validate, \
             patch('app.api.endpoints.wechat_auth.WeChatAuthService.check_existing_users') as mock_check, \
             patch('app.api.endpoints.wechat_auth.WeChatAuthService.process_bind_logic') as mock_process:
            
            # 模拟现有用户（未绑定微信）
            existing_user = User(id=1, username=self.test_username, wechat_openid=None)
            mock_validate.return_value = None
            mock_check.return_value = (existing_user, None)
            mock_process.return_value = (existing_user, "微信账号绑定成功")
            
            response = self.client.post(
                f"{self.base_url}/bind",
                json={
                    "openid": "new_openid_789",
                    "username": self.test_username,
                    "code": self.test_code
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["message"] == "微信账号绑定成功"
            print(f"✅ 现有用户绑定测试通过: {data['message']}")

    def test_duplicate_openid_binding(self, mock_sms_verification, mock_wechat_service):
        """测试重复OpenID绑定场景"""
        with patch('app.api.endpoints.wechat_auth.WeChatAuthService.validate_bind_request') as mock_validate:
            # 模拟OpenID已绑定其他用户的情况
            mock_validate.side_effect = WeChatErrors.OPENID_ALREADY_BOUND
            
            response = self.client.post(
                f"{self.base_url}/bind",
                json={
                    "openid": self.test_openid,
                    "username": "13900139000",
                    "code": self.test_code
                }
            )
            
            assert response.status_code == 400
            data = response.json()
            assert "该微信账号已绑定其他用户" in data["detail"]
            print(f"✅ 重复OpenID绑定测试通过: {data['detail']}")

    def test_invalid_verification_code(self):
        """测试无效验证码场景"""
        with patch('app.api.endpoints.wechat_auth.WeChatAuthService.validate_bind_request') as mock_validate:
            # 模拟验证码无效的情况
            mock_validate.side_effect = WeChatErrors.INVALID_VERIFICATION_CODE
            
            response = self.client.post(
                f"{self.base_url}/bind",
                json={
                    "openid": "test_openid_invalid",
                    "username": self.test_username,
                    "code": "000000"
                }
            )
            
            assert response.status_code == 400
            data = response.json()
            assert "验证码无效或已过期" in data["detail"]
            print(f"✅ 无效验证码测试通过: {data['detail']}")

    def test_qr_code_creation(self):
        """测试二维码创建"""
        with patch('app.api.endpoints.wechat_auth.qr_login_service.create_login_qr_code') as mock_create:
            mock_create.return_value = {
                "scene_str": "login_123456",
                "qr_url": "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=xxx",
                "expire_seconds": 600
            }
            
            response = self.client.post(f"{self.base_url}/qr-login/create")
            
            assert response.status_code == 200
            data = response.json()
            assert "scene_str" in data
            assert "qr_url" in data
            assert "expire_seconds" in data
            print(f"✅ 二维码创建测试通过: {data['scene_str']}")

    def test_wechat_info_endpoint(self):
        """测试获取微信信息接口"""
        with patch('app.api.deps.get_current_user') as mock_user:
            # 模拟已登录用户
            mock_user.return_value = User(
                id=1,
                username=self.test_username,
                wechat_openid=self.test_openid,
                wechat_nickname="测试用户"
            )
            
            response = self.client.get(
                f"{self.base_url}/info",
                headers={"Authorization": "Bearer test_token"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "is_bound" in data
            assert data["is_bound"] == True
            print(f"✅ 微信信息获取测试通过: {data}")

    def test_unbind_wechat(self):
        """测试微信解绑"""
        with patch('app.api.deps.get_current_user') as mock_user, \
             patch('app.api.endpoints.wechat_auth.WeChatAuthService.unbind_wechat') as mock_unbind:
            
            # 模拟已绑定微信的用户
            mock_user.return_value = User(
                id=1,
                username=self.test_username,
                wechat_openid=self.test_openid
            )
            
            mock_unbind.return_value = {
                "message": "微信账号解绑成功",
                "user": {
                    "id": 1,
                    "username": self.test_username,
                    "nickname": None,
                    "wechat_nickname": None,
                    "avatar": None
                }
            }
            
            response = self.client.post(
                f"{self.base_url}/unbind",
                headers={"Authorization": "Bearer test_token"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["message"] == "微信账号解绑成功"
            print(f"✅ 微信解绑测试通过: {data['message']}")


async def test_wechat_bind_scenarios():
    """测试微信绑定的各种场景（兼容性保留）"""
    async with httpx.AsyncClient() as client:
        print("=== 微信绑定功能测试 ===")
        print(f"测试时间: {datetime.now()}")
        print(f"测试服务器: {BASE_URL}")
        print()
        
        # 场景1: 新用户注册（openid和username都不存在）
        print("场景1: 新用户注册")
        try:
            response = await client.post(
                f"{BASE_URL}/api/v1/wechat/bind",
                json={
                    "openid": TEST_OPENID,
                    "username": TEST_USERNAME,
                    "code": TEST_CODE
                }
            )
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                print("✅ 新用户注册成功")
            else:
                print(f"❌ 请求失败: {response.text}")
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        print()
        
        # 场景2: 重复绑定（openid和username都已存在且匹配）
        print("场景2: 重复绑定相同信息")
        try:
            response = await client.post(
                f"{BASE_URL}/api/v1/wechat/bind",
                json={
                    "openid": TEST_OPENID,
                    "username": TEST_USERNAME,
                    "code": TEST_CODE
                }
            )
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                print("✅ 微信信息更新成功")
            else:
                print(f"❌ 请求失败: {response.text}")
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        print()
        
        # 场景3: 尝试绑定不同的用户名到已存在的openid
        print("场景3: 尝试绑定不同用户名到已存在的openid")
        try:
            response = await client.post(
                f"{BASE_URL}/api/v1/wechat/bind",
                json={
                    "openid": TEST_OPENID,
                    "username": "13900139000",  # 不同的手机号
                    "code": TEST_CODE
                }
            )
            print(f"状态码: {response.status_code}")
            if response.status_code == 400:
                data = response.json()
                print(f"响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
                print("✅ 正确拒绝了冲突的绑定")
            else:
                print(f"❌ 应该返回400错误: {response.text}")
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        print()
        
        print("=== 测试完成 ===")


def run_tests():
    """运行所有测试"""
    test_instance = TestWeChatAuth()
    test_instance.setup_method()
    
    print("🚀 开始微信认证功能测试...\n")
    
    try:
        # 运行各项测试
        print("测试1: 新用户注册")
        test_instance.test_new_user_registration(
            test_instance.mock_sms_verification(),
            test_instance.mock_wechat_service()
        )
        
        print("\n测试2: 现有用户绑定微信")
        test_instance.test_existing_user_bind(
            test_instance.mock_sms_verification(),
            test_instance.mock_wechat_service()
        )
        
        print("\n测试3: 重复OpenID绑定")
        test_instance.test_duplicate_openid_binding(
            test_instance.mock_sms_verification(),
            test_instance.mock_wechat_service()
        )
        
        print("\n测试4: 无效验证码")
        test_instance.test_invalid_verification_code()
        
        print("\n测试5: 二维码创建")
        test_instance.test_qr_code_creation()
        
        print("\n测试6: 获取微信信息")
        test_instance.test_wechat_info_endpoint()
        
        print("\n测试7: 微信解绑")
        test_instance.test_unbind_wechat()
        
        print("\n🎉 所有测试通过！")
        print("\n📋 测试总结:")
        print("- ✅ 新用户注册和绑定")
        print("- ✅ 现有用户绑定微信")
        print("- ✅ 重复绑定检测")
        print("- ✅ 验证码验证")
        print("- ✅ 二维码登录")
        print("- ✅ 微信信息获取")
        print("- ✅ 微信账号解绑")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise


if __name__ == "__main__":
    # 可以选择运行单元测试或集成测试
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "integration":
        asyncio.run(test_wechat_bind_scenarios())
    else:
        run_tests()