"""initial

Revision ID: 7130a5b5010b
Revises: 
Create Date: 2025-06-26 09:35:15.899082

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7130a5b5010b'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('categories',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('description', sa.String(length=255), nullable=True),
    sa.Column('parent_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['parent_id'], ['categories.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_categories_id'), 'categories', ['id'], unique=False)
    op.create_index(op.f('ix_categories_name'), 'categories', ['name'], unique=True)
    op.create_table('content_similarities',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('source_content_type', sa.String(length=20), nullable=False),
    sa.Column('source_content_id', sa.Integer(), nullable=False),
    sa.Column('target_content_type', sa.String(length=20), nullable=False),
    sa.Column('target_content_id', sa.Integer(), nullable=False),
    sa.Column('similarity_score', sa.Float(), nullable=False),
    sa.Column('similarity_type', sa.String(length=50), nullable=False),
    sa.Column('calculated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('source_content_type', 'source_content_id', 'target_content_type', 'target_content_id', 'similarity_type', name='uq_content_similarity')
    )
    op.create_index(op.f('ix_content_similarities_id'), 'content_similarities', ['id'], unique=False)
    op.create_index(op.f('ix_content_similarities_similarity_score'), 'content_similarities', ['similarity_score'], unique=False)
    op.create_index(op.f('ix_content_similarities_similarity_type'), 'content_similarities', ['similarity_type'], unique=False)
    op.create_index(op.f('ix_content_similarities_source_content_id'), 'content_similarities', ['source_content_id'], unique=False)
    op.create_index(op.f('ix_content_similarities_source_content_type'), 'content_similarities', ['source_content_type'], unique=False)
    op.create_index(op.f('ix_content_similarities_target_content_id'), 'content_similarities', ['target_content_id'], unique=False)
    op.create_index(op.f('ix_content_similarities_target_content_type'), 'content_similarities', ['target_content_type'], unique=False)
    op.create_table('file_hashes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('file_path', sa.String(length=255), nullable=False),
    sa.Column('file_hash', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('file_hash', name='uq_file_path')
    )
    op.create_index(op.f('ix_file_hashes_file_hash'), 'file_hashes', ['file_hash'], unique=False)
    op.create_index(op.f('ix_file_hashes_file_path'), 'file_hashes', ['file_path'], unique=False)
    op.create_index(op.f('ix_file_hashes_id'), 'file_hashes', ['id'], unique=False)
    op.create_table('permission',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=True, comment='权限名称'),
    sa.Column('code', sa.String(length=50), nullable=True, comment='权限代码'),
    sa.Column('resource', sa.String(length=50), nullable=True, comment='资源类型'),
    sa.Column('action', sa.String(length=50), nullable=True, comment='操作类型：read, write, delete, admin等'),
    sa.Column('description', sa.Text(), nullable=True, comment='权限描述'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否启用'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_permission_code'), 'permission', ['code'], unique=True)
    op.create_index(op.f('ix_permission_id'), 'permission', ['id'], unique=False)
    op.create_index(op.f('ix_permission_name'), 'permission', ['name'], unique=True)
    op.create_index(op.f('ix_permission_resource'), 'permission', ['resource'], unique=False)
    op.create_table('tags',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tags_id'), 'tags', ['id'], unique=False)
    op.create_index(op.f('ix_tags_name'), 'tags', ['name'], unique=True)
    op.create_table('user_role',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=True),
    sa.Column('desc', sa.Text(), nullable=True),
    sa.Column('is_default', sa.Boolean(), nullable=True, comment='是否为默认角色'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否启用'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_role_id'), 'user_role', ['id'], unique=False)
    op.create_index(op.f('ix_user_role_name'), 'user_role', ['name'], unique=True)
    op.create_table('role_permission',
    sa.Column('role_id', sa.Integer(), nullable=False),
    sa.Column('permission_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['permission_id'], ['permission.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['user_role.id'], ),
    sa.PrimaryKeyConstraint('role_id', 'permission_id')
    )
    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('username', sa.String(length=50), nullable=False),
    sa.Column('password', sa.String(length=100), nullable=True),
    sa.Column('role_id', sa.Integer(), nullable=True),
    sa.Column('nickname', sa.String(length=50), nullable=True),
    sa.Column('email', sa.String(length=100), nullable=True),
    sa.Column('avatar', sa.String(length=255), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_superuser', sa.Boolean(), nullable=True),
    sa.Column('last_login', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('wechat_openid', sa.String(length=100), nullable=True, comment='微信OpenID'),
    sa.Column('wechat_unionid', sa.String(length=100), nullable=True, comment='微信UnionID'),
    sa.Column('wechat_nickname', sa.String(length=100), nullable=True, comment='微信昵称'),
    sa.Column('wechat_avatar', sa.String(length=255), nullable=True, comment='微信头像'),
    sa.Column('login_type', sa.String(length=20), nullable=True, comment='登录方式：password, wechat'),
    sa.ForeignKeyConstraint(['role_id'], ['user_role.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_role_id'), 'users', ['role_id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_index(op.f('ix_users_wechat_openid'), 'users', ['wechat_openid'], unique=True)
    op.create_index(op.f('ix_users_wechat_unionid'), 'users', ['wechat_unionid'], unique=True)
    op.create_table('articles',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('cover_url', sa.String(length=512), nullable=True, comment='封面图URL'),
    sa.Column('author_id', sa.Integer(), nullable=False),
    sa.Column('is_published', sa.Boolean(), nullable=True, comment='是否发布 如果未发布则显示到草稿箱中'),
    sa.Column('is_approved', sa.Boolean(), nullable=True, comment='是否通过审核 仅在发布后需要审核'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('category_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['author_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['category_id'], ['categories.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_articles_author_id'), 'articles', ['author_id'], unique=False)
    op.create_index(op.f('ix_articles_id'), 'articles', ['id'], unique=False)
    op.create_index(op.f('ix_articles_title'), 'articles', ['title'], unique=False)
    op.create_table('favorites',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('content_type', sa.String(length=20), nullable=False),
    sa.Column('content_id', sa.Integer(), nullable=False),
    sa.Column('note', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'content_type', 'content_id', name='uq_user_content_favorite')
    )
    op.create_index(op.f('ix_favorites_content_id'), 'favorites', ['content_id'], unique=False)
    op.create_index(op.f('ix_favorites_content_type'), 'favorites', ['content_type'], unique=False)
    op.create_index(op.f('ix_favorites_created_at'), 'favorites', ['created_at'], unique=False)
    op.create_index(op.f('ix_favorites_id'), 'favorites', ['id'], unique=False)
    op.create_index(op.f('ix_favorites_is_active'), 'favorites', ['is_active'], unique=False)
    op.create_index(op.f('ix_favorites_user_id'), 'favorites', ['user_id'], unique=False)
    op.create_table('likes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('content_type', sa.String(length=20), nullable=False),
    sa.Column('content_id', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'content_type', 'content_id', name='uq_user_content_like')
    )
    op.create_index(op.f('ix_likes_content_id'), 'likes', ['content_id'], unique=False)
    op.create_index(op.f('ix_likes_content_type'), 'likes', ['content_type'], unique=False)
    op.create_index(op.f('ix_likes_created_at'), 'likes', ['created_at'], unique=False)
    op.create_index(op.f('ix_likes_id'), 'likes', ['id'], unique=False)
    op.create_index(op.f('ix_likes_is_active'), 'likes', ['is_active'], unique=False)
    op.create_index(op.f('ix_likes_user_id'), 'likes', ['user_id'], unique=False)
    op.create_table('recommendation_logs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('algorithm_type', sa.String(length=50), nullable=False),
    sa.Column('recommended_items', sa.Text(), nullable=False, comment='推荐内容JSON'),
    sa.Column('recommendation_reason', sa.Text(), nullable=True, comment='推荐原因JSON'),
    sa.Column('position', sa.String(length=50), nullable=True, comment='推荐位置'),
    sa.Column('is_clicked', sa.Boolean(), nullable=True),
    sa.Column('clicked_content_id', sa.Integer(), nullable=True),
    sa.Column('clicked_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_recommendation_logs_algorithm_type'), 'recommendation_logs', ['algorithm_type'], unique=False)
    op.create_index(op.f('ix_recommendation_logs_clicked_content_id'), 'recommendation_logs', ['clicked_content_id'], unique=False)
    op.create_index(op.f('ix_recommendation_logs_created_at'), 'recommendation_logs', ['created_at'], unique=False)
    op.create_index(op.f('ix_recommendation_logs_id'), 'recommendation_logs', ['id'], unique=False)
    op.create_index(op.f('ix_recommendation_logs_is_clicked'), 'recommendation_logs', ['is_clicked'], unique=False)
    op.create_index(op.f('ix_recommendation_logs_position'), 'recommendation_logs', ['position'], unique=False)
    op.create_index(op.f('ix_recommendation_logs_user_id'), 'recommendation_logs', ['user_id'], unique=False)
    op.create_table('reviews',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('content_type', sa.Enum('article', 'video', name='content_type'), nullable=False),
    sa.Column('content_id', sa.Integer(), nullable=False),
    sa.Column('status', sa.Enum('pending', 'approved', 'rejected', name='review_status'), nullable=True),
    sa.Column('reviewer_id', sa.Integer(), nullable=True),
    sa.Column('comment', sa.Text(), nullable=True),
    sa.Column('reviewed_at', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['reviewer_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_reviews_content_id'), 'reviews', ['content_id'], unique=False)
    op.create_index(op.f('ix_reviews_content_type'), 'reviews', ['content_type'], unique=False)
    op.create_index(op.f('ix_reviews_id'), 'reviews', ['id'], unique=False)
    op.create_index(op.f('ix_reviews_reviewer_id'), 'reviews', ['reviewer_id'], unique=False)
    op.create_index(op.f('ix_reviews_status'), 'reviews', ['status'], unique=False)
    op.create_table('user_browse_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('content_type', sa.String(length=20), nullable=False),
    sa.Column('content_id', sa.Integer(), nullable=False),
    sa.Column('duration', sa.Integer(), nullable=True, comment='浏览时长（秒）'),
    sa.Column('source', sa.String(length=50), nullable=True, comment='浏览来源'),
    sa.Column('device_type', sa.String(length=20), nullable=True, comment='设备类型：mobile, desktop, tablet'),
    sa.Column('ip_address', sa.String(length=45), nullable=True, comment='IP地址'),
    sa.Column('user_agent', sa.Text(), nullable=True, comment='用户代理字符串'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_browse_history_content_id'), 'user_browse_history', ['content_id'], unique=False)
    op.create_index(op.f('ix_user_browse_history_content_type'), 'user_browse_history', ['content_type'], unique=False)
    op.create_index(op.f('ix_user_browse_history_created_at'), 'user_browse_history', ['created_at'], unique=False)
    op.create_index(op.f('ix_user_browse_history_id'), 'user_browse_history', ['id'], unique=False)
    op.create_index(op.f('ix_user_browse_history_source'), 'user_browse_history', ['source'], unique=False)
    op.create_index(op.f('ix_user_browse_history_user_id'), 'user_browse_history', ['user_id'], unique=False)
    op.create_table('user_devices',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('device_fingerprint', sa.String(length=255), nullable=False, comment='设备指纹'),
    sa.Column('device_name', sa.String(length=100), nullable=True, comment='设备名称'),
    sa.Column('device_type', sa.String(length=20), nullable=True, comment='设备类型：mobile, tablet, desktop'),
    sa.Column('ip_address', sa.String(length=45), nullable=True, comment='IP地址'),
    sa.Column('location', sa.String(length=100), nullable=True, comment='地理位置'),
    sa.Column('user_agent', sa.Text(), nullable=True, comment='用户代理字符串'),
    sa.Column('browser_name', sa.String(length=50), nullable=True, comment='浏览器名称'),
    sa.Column('browser_version', sa.String(length=20), nullable=True, comment='浏览器版本'),
    sa.Column('os_name', sa.String(length=50), nullable=True, comment='操作系统名称'),
    sa.Column('os_version', sa.String(length=20), nullable=True, comment='操作系统版本'),
    sa.Column('is_trusted', sa.Boolean(), nullable=True, comment='是否为信任设备'),
    sa.Column('trust_score', sa.Integer(), nullable=True, comment='信任分数(0-100)'),
    sa.Column('login_count', sa.Integer(), nullable=True, comment='登录次数'),
    sa.Column('last_login_at', sa.DateTime(), nullable=False, comment='最后登录时间'),
    sa.Column('first_login_at', sa.DateTime(), nullable=False, comment='首次登录时间'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否活跃'),
    sa.Column('is_blocked', sa.Boolean(), nullable=True, comment='是否被阻止'),
    sa.Column('blocked_reason', sa.String(length=200), nullable=True, comment='阻止原因'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_devices_device_fingerprint'), 'user_devices', ['device_fingerprint'], unique=False)
    op.create_index(op.f('ix_user_devices_id'), 'user_devices', ['id'], unique=False)
    op.create_index(op.f('ix_user_devices_user_id'), 'user_devices', ['user_id'], unique=False)
    op.create_table('user_follow',
    sa.Column('follower_id', sa.Integer(), nullable=False),
    sa.Column('followed_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
    sa.ForeignKeyConstraint(['followed_id'], ['users.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['follower_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('follower_id', 'followed_id')
    )
    op.create_table('user_interactions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('content_type', sa.String(length=20), nullable=False),
    sa.Column('content_id', sa.Integer(), nullable=False),
    sa.Column('interaction_type', sa.String(length=20), nullable=False),
    sa.Column('weight', sa.Float(), nullable=True, comment='交互权重'),
    sa.Column('extra_data', sa.Text(), nullable=True, comment='额外数据'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_interactions_content_id'), 'user_interactions', ['content_id'], unique=False)
    op.create_index(op.f('ix_user_interactions_content_type'), 'user_interactions', ['content_type'], unique=False)
    op.create_index(op.f('ix_user_interactions_created_at'), 'user_interactions', ['created_at'], unique=False)
    op.create_index(op.f('ix_user_interactions_id'), 'user_interactions', ['id'], unique=False)
    op.create_index(op.f('ix_user_interactions_interaction_type'), 'user_interactions', ['interaction_type'], unique=False)
    op.create_index(op.f('ix_user_interactions_user_id'), 'user_interactions', ['user_id'], unique=False)
    op.create_table('user_profiles',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('interest_tags', sa.Text(), nullable=True, comment='兴趣标签JSON'),
    sa.Column('preferred_categories', sa.Text(), nullable=True, comment='偏好分类JSON'),
    sa.Column('active_hours', sa.Text(), nullable=True, comment='活跃时间段JSON'),
    sa.Column('preferred_device', sa.String(length=20), nullable=True, comment='偏好设备类型'),
    sa.Column('content_preference', sa.String(length=20), nullable=True, comment='内容偏好'),
    sa.Column('diversity_preference', sa.Float(), nullable=True, comment='多样性偏好'),
    sa.Column('last_updated', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_profiles_id'), 'user_profiles', ['id'], unique=False)
    op.create_index(op.f('ix_user_profiles_user_id'), 'user_profiles', ['user_id'], unique=True)
    op.create_table('video_folders',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('path', sa.String(length=255), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_video_folders_id'), 'video_folders', ['id'], unique=False)
    op.create_index(op.f('ix_video_folders_path'), 'video_folders', ['path'], unique=False)
    op.create_index(op.f('ix_video_folders_user_id'), 'video_folders', ['user_id'], unique=False)
    op.create_table('article_tags',
    sa.Column('article_id', sa.Integer(), nullable=False),
    sa.Column('tag_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['article_id'], ['articles.id'], ),
    sa.ForeignKeyConstraint(['tag_id'], ['tags.id'], ),
    sa.PrimaryKeyConstraint('article_id', 'tag_id')
    )
    op.create_table('videos',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('url', sa.String(length=512), nullable=False, comment='视频URL'),
    sa.Column('cover_url', sa.String(length=512), nullable=True, comment='封面图URL'),
    sa.Column('duration', sa.Integer(), nullable=True, comment='视频时长（秒）'),
    sa.Column('author_id', sa.Integer(), nullable=False),
    sa.Column('is_published', sa.Boolean(), nullable=True),
    sa.Column('is_approved', sa.Boolean(), nullable=True, comment='是否通过审核 仅在发布后需要审核'),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('category_id', sa.Integer(), nullable=True),
    sa.Column('folder_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['author_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['category_id'], ['categories.id'], ),
    sa.ForeignKeyConstraint(['folder_id'], ['video_folders.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_videos_author_id'), 'videos', ['author_id'], unique=False)
    op.create_index(op.f('ix_videos_folder_id'), 'videos', ['folder_id'], unique=False)
    op.create_index(op.f('ix_videos_id'), 'videos', ['id'], unique=False)
    op.create_index(op.f('ix_videos_title'), 'videos', ['title'], unique=False)
    op.create_table('comments',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('author_id', sa.Integer(), nullable=False),
    sa.Column('comment_type', sa.Enum('article', 'video', name='comment_type'), nullable=False),
    sa.Column('article_id', sa.Integer(), nullable=True),
    sa.Column('video_id', sa.Integer(), nullable=True),
    sa.Column('is_visible', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['article_id'], ['articles.id'], ),
    sa.ForeignKeyConstraint(['author_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['video_id'], ['videos.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_comments_article_id'), 'comments', ['article_id'], unique=False)
    op.create_index(op.f('ix_comments_author_id'), 'comments', ['author_id'], unique=False)
    op.create_index(op.f('ix_comments_comment_type'), 'comments', ['comment_type'], unique=False)
    op.create_index(op.f('ix_comments_id'), 'comments', ['id'], unique=False)
    op.create_index(op.f('ix_comments_video_id'), 'comments', ['video_id'], unique=False)
    op.create_table('video_tags',
    sa.Column('video_id', sa.Integer(), nullable=False),
    sa.Column('tag_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['tag_id'], ['tags.id'], ),
    sa.ForeignKeyConstraint(['video_id'], ['videos.id'], ),
    sa.PrimaryKeyConstraint('video_id', 'tag_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('video_tags')
    op.drop_index(op.f('ix_comments_video_id'), table_name='comments')
    op.drop_index(op.f('ix_comments_id'), table_name='comments')
    op.drop_index(op.f('ix_comments_comment_type'), table_name='comments')
    op.drop_index(op.f('ix_comments_author_id'), table_name='comments')
    op.drop_index(op.f('ix_comments_article_id'), table_name='comments')
    op.drop_table('comments')
    op.drop_index(op.f('ix_videos_title'), table_name='videos')
    op.drop_index(op.f('ix_videos_id'), table_name='videos')
    op.drop_index(op.f('ix_videos_folder_id'), table_name='videos')
    op.drop_index(op.f('ix_videos_author_id'), table_name='videos')
    op.drop_table('videos')
    op.drop_table('article_tags')
    op.drop_index(op.f('ix_video_folders_user_id'), table_name='video_folders')
    op.drop_index(op.f('ix_video_folders_path'), table_name='video_folders')
    op.drop_index(op.f('ix_video_folders_id'), table_name='video_folders')
    op.drop_table('video_folders')
    op.drop_index(op.f('ix_user_profiles_user_id'), table_name='user_profiles')
    op.drop_index(op.f('ix_user_profiles_id'), table_name='user_profiles')
    op.drop_table('user_profiles')
    op.drop_index(op.f('ix_user_interactions_user_id'), table_name='user_interactions')
    op.drop_index(op.f('ix_user_interactions_interaction_type'), table_name='user_interactions')
    op.drop_index(op.f('ix_user_interactions_id'), table_name='user_interactions')
    op.drop_index(op.f('ix_user_interactions_created_at'), table_name='user_interactions')
    op.drop_index(op.f('ix_user_interactions_content_type'), table_name='user_interactions')
    op.drop_index(op.f('ix_user_interactions_content_id'), table_name='user_interactions')
    op.drop_table('user_interactions')
    op.drop_table('user_follow')
    op.drop_index(op.f('ix_user_devices_user_id'), table_name='user_devices')
    op.drop_index(op.f('ix_user_devices_id'), table_name='user_devices')
    op.drop_index(op.f('ix_user_devices_device_fingerprint'), table_name='user_devices')
    op.drop_table('user_devices')
    op.drop_index(op.f('ix_user_browse_history_user_id'), table_name='user_browse_history')
    op.drop_index(op.f('ix_user_browse_history_source'), table_name='user_browse_history')
    op.drop_index(op.f('ix_user_browse_history_id'), table_name='user_browse_history')
    op.drop_index(op.f('ix_user_browse_history_created_at'), table_name='user_browse_history')
    op.drop_index(op.f('ix_user_browse_history_content_type'), table_name='user_browse_history')
    op.drop_index(op.f('ix_user_browse_history_content_id'), table_name='user_browse_history')
    op.drop_table('user_browse_history')
    op.drop_index(op.f('ix_reviews_status'), table_name='reviews')
    op.drop_index(op.f('ix_reviews_reviewer_id'), table_name='reviews')
    op.drop_index(op.f('ix_reviews_id'), table_name='reviews')
    op.drop_index(op.f('ix_reviews_content_type'), table_name='reviews')
    op.drop_index(op.f('ix_reviews_content_id'), table_name='reviews')
    op.drop_table('reviews')
    op.drop_index(op.f('ix_recommendation_logs_user_id'), table_name='recommendation_logs')
    op.drop_index(op.f('ix_recommendation_logs_position'), table_name='recommendation_logs')
    op.drop_index(op.f('ix_recommendation_logs_is_clicked'), table_name='recommendation_logs')
    op.drop_index(op.f('ix_recommendation_logs_id'), table_name='recommendation_logs')
    op.drop_index(op.f('ix_recommendation_logs_created_at'), table_name='recommendation_logs')
    op.drop_index(op.f('ix_recommendation_logs_clicked_content_id'), table_name='recommendation_logs')
    op.drop_index(op.f('ix_recommendation_logs_algorithm_type'), table_name='recommendation_logs')
    op.drop_table('recommendation_logs')
    op.drop_index(op.f('ix_likes_user_id'), table_name='likes')
    op.drop_index(op.f('ix_likes_is_active'), table_name='likes')
    op.drop_index(op.f('ix_likes_id'), table_name='likes')
    op.drop_index(op.f('ix_likes_created_at'), table_name='likes')
    op.drop_index(op.f('ix_likes_content_type'), table_name='likes')
    op.drop_index(op.f('ix_likes_content_id'), table_name='likes')
    op.drop_table('likes')
    op.drop_index(op.f('ix_favorites_user_id'), table_name='favorites')
    op.drop_index(op.f('ix_favorites_is_active'), table_name='favorites')
    op.drop_index(op.f('ix_favorites_id'), table_name='favorites')
    op.drop_index(op.f('ix_favorites_created_at'), table_name='favorites')
    op.drop_index(op.f('ix_favorites_content_type'), table_name='favorites')
    op.drop_index(op.f('ix_favorites_content_id'), table_name='favorites')
    op.drop_table('favorites')
    op.drop_index(op.f('ix_articles_title'), table_name='articles')
    op.drop_index(op.f('ix_articles_id'), table_name='articles')
    op.drop_index(op.f('ix_articles_author_id'), table_name='articles')
    op.drop_table('articles')
    op.drop_index(op.f('ix_users_wechat_unionid'), table_name='users')
    op.drop_index(op.f('ix_users_wechat_openid'), table_name='users')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_role_id'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_table('role_permission')
    op.drop_index(op.f('ix_user_role_name'), table_name='user_role')
    op.drop_index(op.f('ix_user_role_id'), table_name='user_role')
    op.drop_table('user_role')
    op.drop_index(op.f('ix_tags_name'), table_name='tags')
    op.drop_index(op.f('ix_tags_id'), table_name='tags')
    op.drop_table('tags')
    op.drop_index(op.f('ix_permission_resource'), table_name='permission')
    op.drop_index(op.f('ix_permission_name'), table_name='permission')
    op.drop_index(op.f('ix_permission_id'), table_name='permission')
    op.drop_index(op.f('ix_permission_code'), table_name='permission')
    op.drop_table('permission')
    op.drop_index(op.f('ix_file_hashes_id'), table_name='file_hashes')
    op.drop_index(op.f('ix_file_hashes_file_path'), table_name='file_hashes')
    op.drop_index(op.f('ix_file_hashes_file_hash'), table_name='file_hashes')
    op.drop_table('file_hashes')
    op.drop_index(op.f('ix_content_similarities_target_content_type'), table_name='content_similarities')
    op.drop_index(op.f('ix_content_similarities_target_content_id'), table_name='content_similarities')
    op.drop_index(op.f('ix_content_similarities_source_content_type'), table_name='content_similarities')
    op.drop_index(op.f('ix_content_similarities_source_content_id'), table_name='content_similarities')
    op.drop_index(op.f('ix_content_similarities_similarity_type'), table_name='content_similarities')
    op.drop_index(op.f('ix_content_similarities_similarity_score'), table_name='content_similarities')
    op.drop_index(op.f('ix_content_similarities_id'), table_name='content_similarities')
    op.drop_table('content_similarities')
    op.drop_index(op.f('ix_categories_name'), table_name='categories')
    op.drop_index(op.f('ix_categories_id'), table_name='categories')
    op.drop_table('categories')
    # ### end Alembic commands ###
