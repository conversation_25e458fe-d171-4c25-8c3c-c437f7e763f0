"""add_reply_to_id_to_comments

Revision ID: 5524063ab7a6
Revises: 79ba4aafaa23
Create Date: 2025-07-01 10:59:38.957878

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5524063ab7a6'
down_revision: Union[str, None] = '79ba4aafaa23'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 添加reply_to_id字段
    op.add_column('comments', sa.Column('reply_to_id', sa.Integer(), nullable=True))
    op.create_foreign_key(
        'fk_comments_reply_to_id_users',
        'comments',
        'users',
        ['reply_to_id'],
        ['id']
    )


def downgrade() -> None:
    # 删除外键和字段
    op.drop_constraint('fk_comments_reply_to_id_users', 'comments', type_='foreignkey')
    op.drop_column('comments', 'reply_to_id')
