from datetime import datetime

from pydantic import BaseModel, EmailStr


class UserRoleBase(BaseModel):
    id: int
    name: str
    desc: str
    is_default: bool
    is_active: bool
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class UserRoleResponse(BaseModel):
    name: str

    model_config = {"from_attributes": True}


class UserFollow(BaseModel):
    user_id: int  # 被关注的用户ID
    follow_id: int  # 关注的用户ID


class UserBase(BaseModel):
    id: int
    username: str
    role_id: int
    nickname: str | None = None
    description: str | None = None
    email: str | None = None
    avatar: str | None = None
    is_active: bool
    is_superuser: bool
    created_at: datetime
    updated_at: datetime


class UserCreate(BaseModel):
    username: str
    password: str
    nickname: str | None = None
    email: EmailStr | None = None
    role_id: int


class UserUpdate(BaseModel):
    email: EmailStr | None = None
    nickname: str | None = None
    password: str | None = None
    is_active: bool | None = None
    role_id: int | None = None


class UserInDBBase(UserBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


class UserInDB(UserInDBBase):
    password: str


class User(UserBase):
    model_config = {"from_attributes": True}


class UserResponse(UserInDBBase):
    role: UserRoleResponse
    permissions: list[str] = []

    model_config = {"from_attributes": True}
