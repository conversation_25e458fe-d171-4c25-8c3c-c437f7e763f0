"""微信相关的Pydantic模型"""


from pydantic import BaseModel


class QRCodeResponse(BaseModel):
    """二维码响应模型"""
    scene_str: str
    qr_url: str
    expire_seconds: int


class LoginStatusResponse(BaseModel):
    """登录状态响应模型"""
    status: str  # waiting, scanned, confirmed, expired
    message: str | None = None
    user_info: dict | None = None
    access_token: str | None = None
    token_type: str | None = None


class WeChatUserInfo(BaseModel):
    """微信用户信息模型"""
    openid: str
    nickname: str
    sex: int
    province: str
    city: str
    country: str
    headimgurl: str
    privilege: list = []
    unionid: str | None = None


class WeChatLoginRequest(BaseModel):
    """微信登录请求模型"""
    scene_str: str


class WeChatBindRequest(BaseModel):
    """微信绑定请求模型"""
    openid: str
    user_id: int | None = None


class WeChatMessageEvent(BaseModel):
    """微信消息事件模型"""
    ToUserName: str
    FromUserName: str
    CreateTime: int
    MsgType: str
    Event: str | None = None
    EventKey: str | None = None
    Ticket: str | None = None
