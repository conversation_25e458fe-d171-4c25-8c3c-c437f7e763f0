"""应用配置模块"""

from functools import lru_cache

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置设置"""

    MODE: str = Field(default="dev", description="应用运行模式")
    # 应用设置
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Steam游戏数据聚合API"

    # 数据库设置
    DATABASE_URL: str = Field(default="sqlite:///./steam_data.db", description="数据库连接URL")

    # Redis设置
    REDIS_URL: str = Field(default="redis://localhost:6379/0", description="Redis连接URL")

    # Celery设置
    CELERY_BROKER_URL: str = Field(
        default="redis://localhost:6379/1", description="Celery Broker URL"
    )
    CELERY_RESULT_BACKEND: str = Field(
        default="redis://localhost:6379/2", description="Celery Result Backend"
    )

    # 安全设置
    SECRET_KEY: str = Field(
        default="your-secret-key",  # 在生产环境中应该更改
        description="用于JWT令牌加密的密钥",
    )
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 天
    # 阿里云短信服务
    SIGN_NAME: str = Field(default="阿里云短信服务", description="短信签名")
    # 阿里云oss设置
    OSS_ENDPOINT: str = Field(default="your-oss-endpoint", description="OSS Endpoint")
    OSS_REGION: str = Field(default="your-oss-region", description="OSS Region")
    OSS_BUCKET_NAME: str = Field(default="your-oss-bucket-name", description="OSS Bucket名称")
    OSS_MULTIPART_THRESHOLD: int = Field(
        default=5 * 1024 * 1024, description="OSS分片上传阈值（字节）"
    )
    FILE_HASH_EXPIRE: int = Field(
        default=7 * 24 * 3600, description="文件哈希缓存过期时间（秒），默认7天"
    )

    # 微信公众号设置
    WECHAT_APP_ID: str = Field(default="your-wechat-app-id", description="微信公众号AppID")
    WECHAT_APP_SECRET: str = Field(
        default="your-wechat-app-secret", description="微信公众号AppSecret"
    )

    # 微信API相关配置
    WECHAT_ACCESS_TOKEN_URL: str = Field(
        default="https://api.weixin.qq.com/cgi-bin/token", description="获取access_token的URL"
    )
    WECHAT_QR_CREATE_URL: str = Field(
        default="https://api.weixin.qq.com/cgi-bin/qrcode/create", description="创建二维码的URL"
    )
    WECHAT_QR_SHOW_URL: str = Field(
        default="https://mp.weixin.qq.com/cgi-bin/showqrcode", description="显示二维码的URL"
    )
    WECHAT_USER_INFO_URL: str = Field(
        default="https://api.weixin.qq.com/cgi-bin/user/info", description="获取用户信息的URL"
    )

    # 微信二维码配置
    WECHAT_QR_EXPIRE_SECONDS: int = Field(
        default=600, description="二维码过期时间（秒）", ge=60, le=2592000
    )
    WECHAT_QR_REDIS_PREFIX: str = Field(
        default="wechat:qr:", description="Redis中二维码状态的键前缀"
    )

    # 微信安全配置
    WECHAT_MAX_BIND_ATTEMPTS: int = Field(
        default=5, description="最大绑定尝试次数", ge=1, le=10
    )
    WECHAT_BIND_TIME_WINDOW: int = Field(
        default=3600, description="绑定尝试时间窗口（秒）", ge=300, le=86400
    )
    WECHAT_DEBUG_MODE: bool = Field(
        default=False, description="是否开启微信调试模式"
    )

    # 微信公众号消息推送
    WECHAT_MESSAGE_TOKEN: str = Field(
        default="your-wechat-message-token", description="微信公众号消息推送Token"
    )
    WECHAT_MESSAGE_SECRET_KEY: str = Field(
        default="your-wechat-message-secret-key",
        description="微信公众号消息推送SecretKey",
    )

    # Steam API设置
    STEAM_API_KEY: str = Field(default="your-steam-api-key", description="Steam API密钥")

    # 数据初始化设置
    INIT_VIDEO_FOLDERS: bool = Field(default=True, description="是否在启动时初始化视频文件夹数据")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        extra = "ignore"  # 忽略额外的字段


# 创建设置实例
settings = Settings()


@lru_cache
def get_settings() -> Settings:
    return Settings()
