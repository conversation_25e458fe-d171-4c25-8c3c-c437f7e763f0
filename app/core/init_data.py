from app.core.config import settings
from app.db.session import SessionLocal
from app.services.logger import get_logger
from app.services.video_folder_service import VideoFolderService

logger = get_logger(__name__)


async def init_video_folders():
    """初始化视频文件夹数据

    在应用启动时执行，确保所有用户都有默认文件夹，并将无文件夹的视频迁移到默认文件夹
    """
    try:
        db = SessionLocal()
        try:
            # 确保所有用户都有默认文件夹
            created_folders = await VideoFolderService.ensure_all_users_have_default_folders(db)
            if created_folders:
                logger.info(f"为 {len(created_folders)} 个用户创建了默认文件夹")

            # 将无文件夹的视频迁移到默认文件夹
            migrated_count = await VideoFolderService.migrate_videos_to_default_folders(db)
            if migrated_count > 0:
                logger.info(f"将 {migrated_count} 个视频迁移到默认文件夹")

            logger.info("视频文件夹数据初始化完成")
        finally:
            await db.close()
    except Exception as e:
        logger.error(f"初始化视频文件夹数据失败: {str(e)}")


async def init_data():
    """初始化应用数据

    在应用启动时执行，包括各种数据初始化操作
    """
    # 初始化视频文件夹数据
    if settings.INIT_VIDEO_FOLDERS:
        await init_video_folders()
