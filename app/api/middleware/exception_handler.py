"""
统一异常处理中间件
"""

import traceback
from typing import Callable

from fastapi import HTTPException, Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from app.services.logger import get_logger

logger = get_logger(__name__)


class ExceptionHandlerMiddleware(BaseHTTPMiddleware):
    """统一异常处理中间件"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            response = await call_next(request)
            return response
        except HTTPException:
            # HTTPException 直接向上传播，不做任何处理
            # 这样手动抛出的 HTTPException 能正确返回给前端
            raise
        except ValueError as e:
            # 参数验证错误，返回 400
            logger.warning(f"参数验证错误 - {request.method} {request.url}: {str(e)}")
            return JSONResponse(
                status_code=400,
                content={"detail": str(e)}
            )
        except Exception as e:
            # 记录详细的错误信息用于调试
            error_detail = f"未处理的异常 - {request.method} {request.url}: {str(e)}"
            logger.error(f"{error_detail}\n{traceback.format_exc()}")
            
            # 返回通用的服务器错误，不暴露内部错误详情
            return JSONResponse(
                status_code=500,
                content={"detail": "内部服务器错误"}
            )


class CustomExceptionHandlerMiddleware(BaseHTTPMiddleware):
    """自定义异常处理中间件，支持更细粒度的异常处理"""

    def __init__(self, app, exception_handlers: dict = None):
        super().__init__(app)
        self.exception_handlers = exception_handlers or {}

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            response = await call_next(request)
            return response
        except HTTPException:
            # HTTPException 直接向上传播
            raise
        except Exception as e:
            # 检查是否有自定义的异常处理器
            exception_type = type(e)
            if exception_type in self.exception_handlers:
                handler = self.exception_handlers[exception_type]
                return await handler(request, e)
            
            # 检查父类异常处理器
            for exc_type, handler in self.exception_handlers.items():
                if isinstance(e, exc_type):
                    return await handler(request, e)
            
            # 默认处理
            return await self._default_exception_handler(request, e)

    async def _default_exception_handler(self, request: Request, exc: Exception) -> JSONResponse:
        """默认异常处理器"""
        if isinstance(exc, ValueError):
            logger.warning(f"参数验证错误 - {request.method} {request.url}: {str(exc)}")
            return JSONResponse(
                status_code=400,
                content={"detail": str(exc)}
            )
        
        # 记录详细错误信息
        error_detail = f"未处理的异常 - {request.method} {request.url}: {str(exc)}"
        logger.error(f"{error_detail}\n{traceback.format_exc()}")
        
        return JSONResponse(
            status_code=500,
            content={"detail": "内部服务器错误"}
        )


# 自定义异常处理器示例
async def phone_already_registered_handler(request: Request, exc) -> JSONResponse:
    """手机号已注册异常处理器"""
    logger.info(f"手机号已注册 - {request.method} {request.url}: {str(exc)}")
    return JSONResponse(
        status_code=400,
        content={"detail": f"该手机号已注册，请直接登录"}
    )


async def wechat_bind_exception_handler(request: Request, exc) -> JSONResponse:
    """微信绑定异常处理器"""
    logger.warning(f"微信绑定异常 - {request.method} {request.url}: {str(exc)}")
    return JSONResponse(
        status_code=400,
        content={"detail": str(exc)}
    )


# 预定义的异常处理器映射
DEFAULT_EXCEPTION_HANDLERS = {
    # 可以在这里添加自定义异常类型和对应的处理器
    # PhoneAlreadyRegisteredError: phone_already_registered_handler,
    # WeChatBindException: wechat_bind_exception_handler,
}


def create_exception_handler_middleware(exception_handlers: dict = None):
    """创建异常处理中间件的工厂函数"""
    handlers = DEFAULT_EXCEPTION_HANDLERS.copy()
    if exception_handlers:
        handlers.update(exception_handlers)
    
    return lambda app: CustomExceptionHandlerMiddleware(app, handlers)
