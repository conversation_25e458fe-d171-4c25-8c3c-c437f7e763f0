"""微信公众号消息推送和扫码登录"""

import hashlib
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_db
from app.api.endpoints.auth import create_access_token
from app.core.config import get_settings
from app.models.user import User
from app.schemas.wechat import (
    LoginStatusResponse,
    QRCodeResponse,
)
from app.services.wechat_service import qr_login_service, wechat_service

settings = get_settings()

router = APIRouter()


def verify_wechat_signature(signature: str, timestamp: str, nonce: str) -> bool:
    """
    验证微信签名

    Args:
        signature: 微信加密签名
        timestamp: 时间戳
        nonce: 随机数

    Returns:
        bool: 验证是否通过
    """
    # 获取微信Token
    token = settings.WECHAT_MESSAGE_TOKEN

    # 将Token、timestamp、nonce三个参数进行字典序排序
    params = [token, timestamp, nonce]
    params.sort()

    # 将三个参数字符串拼接成一个字符串进行sha1加密
    temp_str = "".join(params)
    sha1_hash = hashlib.sha1(temp_str.encode("utf-8")).hexdigest()

    # 开发者获得加密后的字符串可与signature对比，标识该请求来源于微信
    return sha1_hash == signature


def parse_wechat_xml(xml_data: str) -> dict:
    """解析微信XML消息"""
    try:
        root = ET.fromstring(xml_data)
        result = {}
        for child in root:
            result[child.tag] = child.text
        return result
    except ET.ParseError:
        return {}


@router.get("/")
async def receive_wechat_message(
    request: Request, signature: str, timestamp: str, nonce: str, echostr: str
) -> str:
    """
    接收微信公众号推送的消息

    微信服务器在向第三方服务器发起请求时，会在请求的query string中携带以下参数：
    - signature: 微信加密签名，signature结合了开发者填写的token参数和请求中的timestamp参数、nonce参数
    - timestamp: 时间戳
    - nonce: 随机数
    - echostr: 随机字符串

    开发者通过检验signature对请求进行校验（下面有校验方式）。
    若确认此次GET请求来自微信服务器，请原样返回echostr参数内容，则接入生效，成为开发者成功，否则接入失败。

    加密/校验流程如下：
    1. 将token、timestamp、nonce三个参数进行字典序排序
    2. 将三个参数字符串拼接成一个字符串进行sha1加密
    3. 开发者获得加密后的字符串可与signature对比，标识该请求来源于微信
    """
    # 验证签名
    if not verify_wechat_signature(signature, timestamp, nonce):
        raise HTTPException(status_code=403, detail="签名验证失败")

    # 验证通过，返回echostr
    return echostr


# 扫码登录相关API
@router.post("/qr-login/create", response_model=QRCodeResponse)
async def create_qr_login():
    """创建登录二维码"""
    try:
        qr_info = await qr_login_service.create_login_qr_code()
        return QRCodeResponse(**qr_info)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建二维码失败: {str(e)}") from e


@router.get("/qr-login/status/{scene_str}", response_model=LoginStatusResponse)
async def get_login_status(scene_str: str, db: AsyncSession = Depends(get_db)):
    """获取登录状态"""
    try:
        status_info = qr_login_service.get_login_status(scene_str)

        # 如果已确认登录，生成JWT token
        if status_info.get("status") == "confirmed" and status_info.get("openid"):
            openid = status_info["openid"]

            # 查找或创建用户
            result = await db.execute(
                select(User).where(User.wechat_openid == openid)
            )
            user = result.scalar_one_or_none()
            
            if user:
                # 更新最后登录时间
                user.last_login = datetime.utcnow()
                await db.commit()

                # 生成访问令牌
                access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
                access_token = create_access_token(
                    data={"sub": user.username}, expires_delta=access_token_expires
                )

                return LoginStatusResponse(
                    status="confirmed",
                    message="登录成功",
                    user_info=status_info.get("user_info"),
                    access_token=access_token,
                    token_type="bearer",
                )
            else:
                # 用户不存在，需要注册
                return LoginStatusResponse(
                    status="need_register",
                    message="用户未注册，需要先注册",
                    user_info=status_info.get("user_info"),
                )

        return LoginStatusResponse(
            status=status_info.get("status", "unknown"),
            message=status_info.get("message"),
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取登录状态失败: {str(e)}") from e


@router.post("/")
async def handle_wechat_event(
    request: Request,
    signature: str,
    timestamp: str,
    nonce: str,
    db: AsyncSession = Depends(get_db),
):
    """处理微信事件消息"""
    # 验证签名
    if not verify_wechat_signature(signature, timestamp, nonce):
        raise HTTPException(status_code=403, detail="签名验证失败")

    # 获取XML数据
    xml_data = await request.body()
    xml_str = xml_data.decode("utf-8")

    # 解析XML消息
    msg_data = parse_wechat_xml(xml_str)
    print("msg_data", msg_data)

    if not msg_data:
        return "success"

    msg_type = msg_data.get("MsgType")
    event = msg_data.get("Event")

    # 处理扫码事件
    if msg_type == "event" and event == "SCAN":
        await handle_scan_event(msg_data, db)
    elif msg_type == "event" and event == "subscribe":
        # 处理关注事件（可能包含扫码）
        await handle_subscribe_event(msg_data, db)

    return "success"


async def handle_scan_event(msg_data: dict, db: AsyncSession):
    """处理扫码事件"""
    openid = msg_data.get("FromUserName")
    event_key = msg_data.get("EventKey")

    if not openid or not event_key:
        return

    # 检查是否是登录二维码
    if event_key.startswith("login_"):
        scene_str = event_key

        # 获取用户信息
        try:
            user_info = await wechat_service.get_user_info(openid)

            # 更新登录状态
            qr_login_service.update_login_status(
                scene_str, "confirmed", openid=openid, user_info=user_info
            )
        except Exception:
            # 如果获取用户信息失败，仍然标记为已扫码
            qr_login_service.update_login_status(scene_str, "scanned", openid=openid)


async def handle_subscribe_event(msg_data: dict, db: AsyncSession):
    """处理关注事件"""
    openid = msg_data.get("FromUserName")
    event_key = msg_data.get("EventKey")

    if not openid:
        return

    # 如果是通过二维码关注的
    if event_key and event_key.startswith("qrscene_login_"):
        scene_str = event_key.replace("qrscene_", "")

        # 获取用户信息
        try:
            user_info = await wechat_service.get_user_info(openid)

            # 更新登录状态
            qr_login_service.update_login_status(
                scene_str, "confirmed", openid=openid, user_info=user_info
            )
        except Exception:
            # 如果获取用户信息失败，仍然标记为已扫码
            qr_login_service.update_login_status(scene_str, "scanned", openid=openid)


@router.post("/register-with-wechat")
async def register_with_wechat(
    openid: str, username: str, nickname: str = None, db: AsyncSession = Depends(get_db)
):
    """使用微信信息注册新用户"""
    try:
        # 检查用户名是否已存在
        result = await db.execute(
            select(User).where(User.username == username)
        )
        existing_user = result.scalar_one_or_none()
        if existing_user:
            raise HTTPException(status_code=400, detail="用户名已存在")

        # 检查微信openid是否已绑定
        result = await db.execute(
            select(User).where(User.wechat_openid == openid)
        )
        existing_wechat_user = result.scalar_one_or_none()
        if existing_wechat_user:
            raise HTTPException(status_code=400, detail="该微信账号已绑定其他用户")

        # 获取微信用户信息
        wechat_user_info = await wechat_service.get_user_info(openid)

        # 创建新用户
        new_user = User(
            username=username,
            nickname=nickname or wechat_user_info.get("nickname", username),
            wechat_openid=openid,
            wechat_unionid=wechat_user_info.get("unionid"),
            wechat_nickname=wechat_user_info.get("nickname"),
            wechat_avatar=wechat_user_info.get("headimgurl"),
            login_type="wechat",
            is_active=True,
        )

        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)

        # 生成访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": new_user.username}, expires_delta=access_token_expires
        )

        return {
            "message": "注册成功",
            "user": {
                "id": new_user.id,
                "username": new_user.username,
                "nickname": new_user.nickname,
                "wechat_nickname": new_user.wechat_nickname,
                "avatar": new_user.wechat_avatar,
            },
            "access_token": access_token,
            "token_type": "bearer",
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"注册失败: {str(e)}") from e
