from datetime import timedelta
from typing import Any

from fastapi import APIRouter, Depends
from passlib.context import Crypt<PERSON>ontext
from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_user
from app.models.user import User

router = APIRouter()

# 密码上下文，用于密码哈希和验证
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class Token(BaseModel):
    access_token: str
    token_type: str


class TokenData(BaseModel):
    username: str | None = None
    device_id: str | None = None


def verify_password(plain_password, hashed_password):
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password):
    """获取密码哈希"""
    return pwd_context.hash(password)


async def authenticate_user(db: AsyncSession, username: str, password: str):
    """认证用户"""
    result = await db.execute(select(User).where(User.username == username))
    user = result.scalar_one_or_none()
    if not user:
        return False
    if not verify_password(password, user.password):
        return False
    return user


async def create_access_token(
    data: dict, expires_delta: timedelta | None = None, device_id: int | None = None
) -> str:
    """创建访问令牌"""
    from app.services.token_service import TokenService

    username = data.get("sub")
    token_type = data.get("type", "access")

    if not username:
        raise ValueError("Token data must contain 'sub' field")

    return await TokenService.create_token(
        username=username, device_id=device_id, expires_delta=expires_delta, token_type=token_type
    )


# @router.post("/login", response_model=Token)
# async def login_for_access_token(
#     form_data: OAuth2PasswordRequestForm = Depends(),
#     db: Session = Depends(get_db),
# ) -> Any:
#     """用户登录获取令牌"""
#     user = authenticate_user(db, form_data.username, form_data.password)
#     if not user:
#         raise HTTPException(
#             status_code=status.HTTP_401_UNAUTHORIZED,
#             detail="用户名或密码错误",
#             headers={"WWW-Authenticate": "Bearer"},
#         )
#     if not user.is_active:
#         raise HTTPException(status_code=400, detail="用户未激活")

#     # 更新最后登录时间
#     user.last_login = datetime.utcnow()
#     db.commit()

#     access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
#     access_token = create_access_token(
#         data={"sub": user.username}, expires_delta=access_token_expires
#     )
#     return {"access_token": access_token, "token_type": "bearer"}


@router.get("/me", response_model=dict)
async def read_users_me(current_user: User = Depends(get_current_user)) -> Any:
    """获取当前用户信息"""
    return {
        "id": current_user.id,
        "username": current_user.username,
        "nickname": current_user.nickname,
        "email": current_user.email,
        "is_active": current_user.is_active,
        "is_superuser": current_user.is_superuser,
        "role_id": current_user.role_id,
        "role": current_user.role.name if current_user.role else None,
        "permissions": current_user.permissions,
    }
