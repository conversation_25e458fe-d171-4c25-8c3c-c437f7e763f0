"""短信验证码认证相关接口"""

from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import BaseModel, Field
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.api import deps
from app.api.endpoints.auth import create_access_token
from app.core.config import settings
from app.core.exceptions import PhoneAlreadyRegisteredError
from app.core.sms_template import SmsTemplate
from app.models.user_device import UserDevice
from app.services.device_service import DeviceTrustService
from app.services.logger import get_logger
from app.services.sms_verification import SmsVerification
from app.services.user_service import UserService
from app.services.validate_phone_number import validate_phone_number

logger = get_logger(__name__)
router = APIRouter()
sms_service = SmsVerification()
device_service = DeviceTrustService()


class PhoneNumber(BaseModel):
    """手机号请求模型"""

    phone: str = Field(..., description="手机号码")


class VerifyCode(PhoneNumber):
    """验证码验证请求模型"""

    code: str = Field(..., min_length=6, max_length=6, description="验证码")


class UserInfo(BaseModel):
    """用户信息响应模型"""

    id: int
    username: str
    nickname: str | None = None
    email: str | None = None
    avatar: str | None = None
    is_active: bool
    last_login: datetime | None = None
    created_at: datetime

    class Config:
        from_attributes = True


class AuthResponse(BaseModel):
    """认证响应模型"""

    access_token: str
    token_type: str = "bearer"
    user: UserInfo


class DeviceVerificationResponse(BaseModel):
    """设备验证响应模型"""

    requires_device_verification: bool = True
    message: str
    device_info: dict | None = None
    verification_token: str | None = None


class DeviceVerifyCode(VerifyCode):
    """设备验证码验证请求模型"""

    verification_token: str = Field(..., description="设备验证令牌")


@router.post("/send-login-code")
async def send_login_code(
    *, phone_in: PhoneNumber, db: AsyncSession = Depends(deps.get_db)
) -> dict:
    """发送登录验证码

    Args:
        phone_in: 手机号请求模型
        db: 数据库会话

    Returns:
        dict: 发送结果

    Raises:
        HTTPException: 请求异常
    """
    try:
        # 验证手机号格式
        phone = validate_phone_number(phone_in.phone)

        # 发送验证码
        await sms_service.send_verification_code(phone, SmsTemplate.LOGIN)

        return {"message": "验证码已发送"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e
    except Exception as e:
        logger.error(f"发送登录验证码失败：{str(e)}")
        raise HTTPException(status_code=500, detail="发送验证码失败，请稍后重试") from e


@router.post("/send-register-code")
async def send_register_code(
    *, phone_in: PhoneNumber, db: AsyncSession = Depends(deps.get_db)
) -> dict:
    """发送注册验证码

    Args:
        phone_in: 手机号请求模型
        db: 数据库会话

    Returns:
        dict: 发送结果

    Raises:
        HTTPException: 请求异常
    """
    try:
        # 验证手机号格式
        phone = validate_phone_number(phone_in.phone)

        # 检查手机号是否已注册（使用username字段存储手机号）
        if await UserService.is_phone_registered(db, phone):
            raise PhoneAlreadyRegisteredError(phone)

        # 发送验证码
        await sms_service.send_verification_code(phone, SmsTemplate.REGISTER)
        return {"message": "验证码已发送"}
    except PhoneAlreadyRegisteredError as e:
        raise HTTPException(status_code=400, detail="该手机号已注册，请直接登录") from e
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e
    except Exception as e:
        logger.error(f"发送注册验证码失败：{str(e)}")
        raise HTTPException(status_code=500, detail="发送验证码失败，请稍后重试") from e


@router.post("/verify-login-code", response_model=AuthResponse | DeviceVerificationResponse)
async def verify_login_code(
    *, verify_in: VerifyCode, request: Request, db: AsyncSession = Depends(deps.get_db)
) -> AuthResponse | DeviceVerificationResponse:
    """验证登录验证码

    Args:
        verify_in: 验证码验证请求模型
        db: 数据库会话

    Returns:
        dict: 验证结果

    Raises:
        HTTPException: 请求异常
    """
    try:
        # 验证手机号格式
        phone = validate_phone_number(verify_in.phone)

        # 验证验证码
        if await sms_service.verify_code(phone, verify_in.code, SmsTemplate.LOGIN):
            # 查找用户
            user = await UserService.get_user_by_username(db, phone)
            if not user:
                raise HTTPException(status_code=404, detail="用户不存在，请先注册")

            # 检查用户是否激活
            if not user.is_active:
                raise HTTPException(status_code=400, detail="用户未激活")

            # 获取或创建设备记录
            device, is_new_device = await device_service.get_or_create_device(db, user.id, request)

            # 检查是否需要设备验证
            if device_service.requires_verification(device):
                # 生成设备验证令牌
                verification_token = await create_access_token(
                    data={
                        "sub": user.username,
                        "device_id": device.id,
                        "type": "device_verification",
                    },
                    expires_delta=timedelta(minutes=10),  # 10分钟有效期
                    device_id=device.id,
                )

                return DeviceVerificationResponse(
                    requires_device_verification=True,
                    message="检测到新设备登录，需要进行设备验证",
                    device_info={
                        "device_name": device.device_name,
                        "device_type": device.device_type,
                        "location": device.location or "未知位置",
                        "is_new_device": is_new_device,
                    },
                    verification_token=verification_token,
                )

            # 设备可信，直接登录
            # 生成访问令牌
            access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = await create_access_token(
                data={"sub": user.username}, expires_delta=access_token_expires, device_id=device.id
            )

            # 更新最后登录时间
            await UserService.update_last_login(db, user)

            # 返回认证响应
            return AuthResponse(access_token=access_token, user=UserInfo.model_validate(user))
        else:
            raise HTTPException(status_code=400, detail="验证码错误")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e
    except Exception as e:
        logger.error(f"验证登录验证码失败：{str(e)}")
        raise HTTPException(status_code=500, detail="验证失败，请稍后重试") from e


@router.post("/verify-register-code", response_model=AuthResponse)
async def verify_register_code(
    *, request: Request, verify_in: VerifyCode, db: AsyncSession = Depends(deps.get_db)
) -> AuthResponse:
    """验证注册验证码

    Args:
        verify_in: 验证码验证请求模型
        db: 数据库会话

    Returns:
        dict: 验证结果

    Raises:
        HTTPException: 请求异常
    """
    try:
        # 验证手机号格式
        phone = validate_phone_number(verify_in.phone)
        is_pass = await sms_service.verify_code(phone, verify_in.code, SmsTemplate.REGISTER)
        # 验证验证码
        if is_pass:
            # 检查手机号是否已注册（使用username字段存储手机号）
            if await UserService.is_phone_registered(db, phone):
                raise HTTPException(status_code=400, detail="该手机号已注册，请直接登录")

            # 创建新用户
            user = await UserService.create_user_by_phone(db, phone)

            # 获取或创建设备记录
            device, _ = await device_service.get_or_create_device(db, user.id, request)

            # 生成访问令牌
            access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = await create_access_token(
                data={"sub": user.username}, expires_delta=access_token_expires, device_id=device.id
            )

            # 更新最后登录时间
            await UserService.update_last_login(db, user)

            # 首次注册，直接信任设备
            await device_service.trust_device(db, device)

            # 返回认证响应
            return AuthResponse(access_token=access_token, user=UserInfo.model_validate(user))
        else:
            raise HTTPException(status_code=400, detail="验证码错误")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e
    except Exception as e:
        logger.error(f"验证注册验证码失败：{str(e)}")
        raise HTTPException(status_code=500, detail="验证失败，请稍后重试") from e


@router.post("/send-device-verification-code")
async def send_device_verification_code(
    *, verification_token: str, db: AsyncSession = Depends(deps.get_db)
) -> dict:
    """发送设备验证码

    Args:
        verification_token: 设备验证令牌
        db: 数据库会话

    Returns:
        dict: 发送结果

    Raises:
        HTTPException: 请求异常
    """
    try:
        from jose import JWTError, jwt

        # 验证令牌
        try:
            payload = jwt.decode(verification_token, settings.SECRET_KEY, algorithms=["HS256"])
            username = payload.get("sub")
            device_id = payload.get("device_id")
            token_type = payload.get("type")

            if not username or not device_id or token_type != "device_verification":
                raise HTTPException(status_code=400, detail="无效的验证令牌")

        except JWTError as e:
            raise HTTPException(status_code=400, detail="验证令牌已过期或无效") from e

        # 获取用户
        user = await UserService.get_user_by_username(db, username)
        if not user or not user.username:
            raise HTTPException(status_code=404, detail="用户不存在或未绑定用户名")

        # 发送设备验证码
        await sms_service.send_verification_code(user.username, SmsTemplate.DEVICE_VERIFICATION)

        return {"message": "设备验证码已发送"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发送设备验证码失败：{str(e)}")
        raise HTTPException(status_code=500, detail="发送验证码失败，请稍后重试") from e


@router.post("/verify-device-code", response_model=AuthResponse)
async def verify_device_code(
    *, verify_in: DeviceVerifyCode, db: AsyncSession = Depends(deps.get_db)
) -> AuthResponse:
    """验证设备验证码

    Args:
        verify_in: 设备验证码验证请求模型
        db: 数据库会话

    Returns:
        AuthResponse: 认证响应

    Raises:
        HTTPException: 请求异常
    """
    try:
        from jose import JWTError, jwt

        # 验证手机号格式
        phone = validate_phone_number(verify_in.phone)

        # 验证令牌
        try:
            payload = jwt.decode(
                verify_in.verification_token, settings.SECRET_KEY, algorithms=["HS256"]
            )
            username = payload.get("sub")
            device_id = payload.get("device_id")
            token_type = payload.get("type")

            if not username or not device_id or token_type != "device_verification":
                raise HTTPException(status_code=400, detail="无效的验证令牌")

        except JWTError as e:
            raise HTTPException(status_code=400, detail="验证令牌已过期或无效") from e

        # 验证验证码
        if await sms_service.verify_code(phone, verify_in.code, SmsTemplate.DEVICE_VERIFICATION):
            # 获取用户
            user = await UserService.get_user_by_username(db, username)
            if not user:
                raise HTTPException(status_code=404, detail="用户不存在")

            # 获取设备并标记为信任
            result = await db.execute(select(UserDevice).filter(UserDevice.id == device_id))
            device = result.scalar_one_or_none()
            if device and device.user_id == user.id:
                await device_service.trust_device(db, device)

            # 生成访问令牌
            access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
            access_token = await create_access_token(
                data={"sub": user.username}, expires_delta=access_token_expires, device_id=device.id
            )

            # 更新最后登录时间
            await UserService.update_last_login(db, user)

            # 返回认证响应
            return AuthResponse(access_token=access_token, user=UserInfo.model_validate(user))
        else:
            raise HTTPException(status_code=400, detail="验证码错误")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证设备验证码失败：{str(e)}")
        raise HTTPException(status_code=500, detail="验证失败，请稍后重试") from e
