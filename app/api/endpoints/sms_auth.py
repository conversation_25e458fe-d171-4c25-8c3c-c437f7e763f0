"""短信验证码认证相关接口"""

from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession

from app.api import deps
from app.core.exceptions import PhoneAlreadyRegisteredError
from app.schemas.unified_auth import UnifiedAuthResponse, UnifiedDeviceVerificationResponse
from app.services.logger import get_logger
from app.services.sms_auth_service import sms_auth_service
from app.services.unified_auth_adapter import unified_auth_adapter

logger = get_logger(__name__)
router = APIRouter()  # SMS 认证服务已在导入时初始化


class PhoneNumber(BaseModel):
    """手机号请求模型"""

    phone: str = Field(..., description="手机号码")


class VerifyCode(PhoneNumber):
    """验证码验证请求模型"""

    code: str = Field(..., min_length=6, max_length=6, description="验证码")


class UserInfo(BaseModel):
    """用户信息响应模型"""

    id: int
    username: str
    nickname: str | None = None
    email: str | None = None
    avatar: str | None = None
    is_active: bool
    last_login: datetime | None = None
    created_at: datetime

    class Config:
        from_attributes = True


class AuthResponse(BaseModel):
    """认证响应模型"""

    access_token: str
    token_type: str = "bearer"
    user: UserInfo


class DeviceVerificationResponse(BaseModel):
    """设备验证响应模型"""

    requires_device_verification: bool = True
    message: str
    device_info: dict | None = None
    verification_token: str | None = None


class DeviceVerifyCodeIn(BaseModel):
    verification_token: str = Field(..., description="设备验证令牌")


class DeviceVerifyCode(VerifyCode):
    """设备验证码验证请求模型"""

    verification_token: str = Field(..., description="设备验证令牌")


@router.post("/send-auth-code")
async def send_auth_code(*, phone_in: PhoneNumber, db: AsyncSession = Depends(deps.get_db)) -> dict:
    """发送登录/注册验证码（统一接口）

    Args:
        phone_in: 手机号请求模型
        db: 数据库会话

    Returns:
        dict: 发送结果，包含用户状态信息

    Raises:
        HTTPException: 请求异常
    """
    try:
        # 使用 SMS 认证服务发送验证码
        result = await sms_auth_service.send_auth_code(phone_in.phone, db)
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e
    except Exception as e:
        logger.error(f"发送验证码失败：{str(e)}")
        raise HTTPException(status_code=500, detail="发送验证码失败，请稍后重试") from e


@router.post("/send-register-code")
async def send_register_code(
    *, phone_in: PhoneNumber, db: AsyncSession = Depends(deps.get_db)
) -> dict:
    """发送注册验证码（保留兼容性）

    Args:
        phone_in: 手机号请求模型
        db: 数据库会话

    Returns:
        dict: 发送结果

    Raises:
        HTTPException: 请求异常
    """
    try:
        # 使用 SMS 认证服务发送注册验证码
        result = await sms_auth_service.send_register_code(phone_in.phone, db)
        return result
    except PhoneAlreadyRegisteredError as e:
        raise HTTPException(status_code=400, detail="该手机号已注册，请直接登录") from e
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e
    except Exception as e:
        logger.error(f"发送注册验证码失败：{str(e)}")
        raise HTTPException(status_code=500, detail="发送验证码失败，请稍后重试") from e


@router.post(
    "/verify-auth-code", response_model=UnifiedAuthResponse | UnifiedDeviceVerificationResponse
)
async def verify_auth_code(
    *, verify_in: VerifyCode, request: Request, db: AsyncSession = Depends(deps.get_db)
) -> UnifiedAuthResponse | UnifiedDeviceVerificationResponse:
    """验证登录/注册验证码（统一接口，自动注册）

    Args:
        verify_in: 验证码验证请求模型
        request: HTTP请求对象
        db: 数据库会话

    Returns:
        AuthResponse | DeviceVerificationResponse: 认证响应或设备验证响应

    Raises:
        HTTPException: 请求异常
    """
    try:
        # 使用 SMS 认证服务处理认证验证
        result = await sms_auth_service.handle_auth_verification(
            verify_in.phone, verify_in.code, request, db
        )

        # 转换为统一认证结果
        unified_result = unified_auth_adapter.from_sms_auth_result(result)

        # 如果需要设备验证
        if unified_result.requires_device_verification:
            return unified_result.to_device_verification_response()

        # 正常登录成功
        return unified_result.to_auth_response()

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e
    except HTTPException:
        # 让 HTTPException 直接向上传播，不被通用异常处理器捕获
        raise
    except Exception as e:
        logger.error(f"验证认证码时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误") from e


@router.post("/send-device-verification-code")
async def send_device_verification_code(
    *, verification_token: DeviceVerifyCodeIn, db: AsyncSession = Depends(deps.get_db)
) -> dict:
    """发送设备验证码

    Args:
        verification_token: 设备验证令牌
        db: 数据库会话

    Returns:
        dict: 发送结果

    Raises:
        HTTPException: 请求异常
    """
    try:
        # 使用 SMS 认证服务发送设备验证码
        result = await sms_auth_service.send_device_verification_code(
            verification_token.verification_token, db
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发送设备验证码失败：{str(e)}")
        raise HTTPException(status_code=500, detail="发送设备验证码失败，请稍后重试") from e


@router.post("/verify-device-code", response_model=UnifiedAuthResponse)
async def verify_device_code(
    *, verify_in: DeviceVerifyCode, db: AsyncSession = Depends(deps.get_db)
) -> UnifiedAuthResponse:
    """验证设备验证码

    Args:
        verify_in: 设备验证码验证请求模型
        db: 数据库会话

    Returns:
        AuthResponse: 认证响应

    Raises:
        HTTPException: 请求异常
    """
    try:
        # 使用 SMS 认证服务验证设备验证码
        result = await sms_auth_service.verify_device_code(
            verify_in.phone, verify_in.code, verify_in.verification_token, db
        )

        # 转换为统一认证结果
        unified_result = unified_auth_adapter.from_sms_auth_result(result)
        return unified_result.to_auth_response()

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e)) from e
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"验证设备验证码时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误") from e
