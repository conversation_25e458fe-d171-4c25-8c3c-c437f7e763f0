from fastapi import APIRouter

from app.api.endpoints import (
    articles,
    auth,
    comments,
    favorites,
    likes,
    recommendations,
    reviews,
    roles,
    sms_auth,
    upload,
    user_behavior,
    user_history,
    users,
    video_folders,
    videos,
)

api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(sms_auth.router, prefix="/sms-auth", tags=["sms-auth"])
# api_router.include_router(token_management.router, prefix="/token", tags=["token-management"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(roles.router, prefix="/roles", tags=["roles"])
api_router.include_router(articles.router, prefix="/articles", tags=["articles"])
api_router.include_router(videos.router, prefix="/videos", tags=["videos"])
api_router.include_router(video_folders.router, prefix="/video-folders", tags=["video-folders"])
api_router.include_router(reviews.router, prefix="/reviews", tags=["reviews"])
api_router.include_router(comments.router, prefix="/comments", tags=["comments"])
api_router.include_router(likes.router, prefix="/likes", tags=["likes"])
api_router.include_router(favorites.router, prefix="/favorites", tags=["favorites"])
api_router.include_router(
    recommendations.router, prefix="/recommendations", tags=["recommendations"]
)
api_router.include_router(user_behavior.router, prefix="/user/behavior", tags=["user-behavior"])
api_router.include_router(user_history.router, prefix="/user/history", tags=["user-history"])
api_router.include_router(upload.router, prefix="/upload", tags=["upload"])
