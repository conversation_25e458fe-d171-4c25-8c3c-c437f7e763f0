from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, ForeignKey, Integer, String, func
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

from app.db.session import Base


class VideoFolder(Base):
    """视频文件夹数据模型"""

    __tablename__ = "video_folders"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    path = Column(
        String(255),
        nullable=False,
        index=True,
        comment="存储路径，格式为：/user_{user_id}/{folder_name}",
    )
    user_id = Column(Integer, ForeignKey("users.id"), index=True, nullable=False)
    # 父文件夹ID，实现文件夹层级结构
    parent_id = Column(Integer, ForeignKey("video_folders.id"), nullable=True, index=True)
    # 是否为默认文件夹（每个用户有且仅有一个默认文件夹）
    is_default = Column(Boolean, default=False, nullable=False)
    # 文件夹权限控制
    is_public = Column(Boolean, default=False, nullable=False, comment="是否公开可见")
    access_level = Column(Integer, default=0, comment="访问级别：0-私有，1-公开，2-链接可见，3-指定用户可见")
    # 文件夹封面
    cover_url = Column(String(512), nullable=True, comment="文件夹封面图URL")
    # 排序权重
    sort_order = Column(Integer, default=0, comment="排序权重，值越大越靠前")
    # 软删除标记
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)

    # 关联关系
    user = relationship("User", back_populates="video_folders")
    videos = relationship("Video", back_populates="folder")
    # 自引用关系，实现父子文件夹
    parent = relationship("VideoFolder", remote_side=[id], backref="children")
    
    # 计算属性：视频数量 - 存储值而非动态计算，避免异步环境问题
    _video_count = Column(Integer, default=0, comment="文件夹中的视频数量")
    
    @property
    def video_count(self):
        # 返回存储的值，避免在异步环境中访问关系属性
        return self._video_count
        
    # 提供一个方法来更新视频数量
    def update_video_count(self, count=None):
        if count is not None:
            self._video_count = count
        else:
            # 仅在显式调用更新方法时计算
            self._video_count = len([v for v in self.videos if not getattr(v, 'is_deleted', False)])
        return self._video_count

    def __repr__(self):
        return f"<VideoFolder {self.name}>"
