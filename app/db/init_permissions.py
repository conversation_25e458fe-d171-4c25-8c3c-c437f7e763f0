from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.user import Permission, User, UserRole


async def init_permissions(db: AsyncSession) -> None:
    """初始化权限数据"""
    # 检查是否已经初始化
    result = await db.execute(select(Permission).limit(1))
    if result.scalar_one_or_none():
        return

    # 创建基本权限
    permissions = [
        # 用户资源权限
        Permission(
            name="查看用户列表",
            code="users:list",
            resource="users",
            action="read",
            description="允许查看用户列表",
        ),
        Permission(
            name="查看用户详情",
            code="users:read",
            resource="users",
            action="read",
            description="允许查看用户详情",
        ),
        Permission(
            name="创建用户",
            code="users:create",
            resource="users",
            action="write",
            description="允许创建新用户",
        ),
        Permission(
            name="更新用户",
            code="users:update",
            resource="users",
            action="write",
            description="允许更新用户信息",
        ),
        Permission(
            name="删除用户",
            code="users:delete",
            resource="users",
            action="delete",
            description="允许删除用户",
        ),
        Permission(
            name="关注用户",
            code="users:follow",
            resource="users",
            action="write",
            description="允许关注其他用户",
        ),
        Permission(
            name="取消关注用户",
            code="users:unfollow",
            resource="users",
            action="write",
            description="允许取消关注其他用户",
        ),
        # 角色资源权限
        Permission(
            name="查看角色列表",
            code="roles:list",
            resource="roles",
            action="read",
            description="允许查看角色列表",
        ),
        Permission(
            name="查看角色详情",
            code="roles:read",
            resource="roles",
            action="read",
            description="允许查看角色详情",
        ),
        Permission(
            name="创建角色",
            code="roles:create",
            resource="roles",
            action="write",
            description="允许创建新角色",
        ),
        Permission(
            name="更新角色",
            code="roles:update",
            resource="roles",
            action="write",
            description="允许更新角色信息",
        ),
        Permission(
            name="删除角色",
            code="roles:delete",
            resource="roles",
            action="delete",
            description="允许删除角色",
        ),
        # 权限资源权限
        Permission(
            name="查看权限列表",
            code="permissions:list",
            resource="permissions",
            action="read",
            description="允许查看权限列表",
        ),
        Permission(
            name="分配权限",
            code="permissions:assign",
            resource="permissions",
            action="write",
            description="允许分配权限给角色",
        ),
        # 文章资源权限
        Permission(
            name="查看公开文章",
            code="articles:read_public",
            resource="articles",
            action="read",
            description="允许查看已发布且已审核的文章",
        ),
        Permission(
            name="查看所有文章",
            code="articles:read_all",
            resource="articles",
            action="read",
            description="允许查看所有文章（管理员权限）",
        ),
        Permission(
            name="查看自己的文章",
            code="articles:read_own",
            resource="articles",
            action="read",
            description="允许查看自己创建的文章",
        ),
        Permission(
            name="查看草稿文章",
            code="articles:read_draft",
            resource="articles",
            action="read",
            description="允许查看草稿状态的文章",
        ),
        Permission(
            name="查看待审核文章",
            code="articles:read_pending",
            resource="articles",
            action="read",
            description="允许查看待审核状态的文章",
        ),
        Permission(
            name="创建文章",
            code="articles:create",
            resource="articles",
            action="write",
            description="允许创建新文章",
        ),
        Permission(
            name="更新自己的文章",
            code="articles:update_own",
            resource="articles",
            action="write",
            description="允许更新自己创建的文章",
        ),
        Permission(
            name="更新所有文章",
            code="articles:update_all",
            resource="articles",
            action="write",
            description="允许更新任何文章（管理员权限）",
        ),
        Permission(
            name="发布自己的文章",
            code="articles:publish_own",
            resource="articles",
            action="publish",
            description="允许发布自己创建的文章",
        ),
        Permission(
            name="发布所有文章",
            code="articles:publish_all",
            resource="articles",
            action="publish",
            description="允许发布任何文章（管理员权限）",
        ),
        Permission(
            name="审核文章",
            code="articles:approve",
            resource="articles",
            action="approve",
            description="允许审核文章（管理员权限）",
        ),
        Permission(
            name="删除自己的文章",
            code="articles:delete_own",
            resource="articles",
            action="delete",
            description="允许删除自己创建的文章",
        ),
        Permission(
            name="删除所有文章",
            code="articles:delete_all",
            resource="articles",
            action="delete",
            description="允许删除任何文章（管理员权限）",
        ),
        Permission(
            name="管理文章",
            code="articles:manage",
            resource="articles",
            action="admin",
            description="允许管理文章系统（管理员权限）",
        ),
    ]

    db.add_all(permissions)
    await db.commit()

    # 创建基本角色
    admin_role = UserRole(
        name="管理员",
        desc="系统管理员，拥有所有权限",
        is_active=True,
    )

    user_role = UserRole(
        name="普通用户",
        desc="普通用户，拥有基本查看权限",
        is_default=True,
        is_active=True,
    )

    guest_role = UserRole(
        name="访客",
        desc="访客用户，只有有限的查看权限",
        is_active=True,
    )

    db.add_all([admin_role, user_role, guest_role])
    await db.commit()

    # 分配权限给角色
    # 管理员拥有所有权限
    admin_role.permissions = permissions

    # 普通用户拥有基本查看和操作权限，但不能管理用户和权限
    user_permissions = [
        p
        for p in permissions
        if p.resource == "games"
        or (p.resource == "users" and p.action == "read")
        or p.code == "users:update"  # 允许更新自己的信息
        or (
            p.resource == "articles"
            and p.code
            in [
                "articles:read_public",
                "articles:read_own",
                "articles:read_draft",
                "articles:read_pending",
                "articles:create",
                "articles:update_own",
                "articles:publish_own",
                "articles:delete_own",
            ]
        )
    ]
    user_role.permissions = user_permissions

    # 访客只有查看公开内容的权限
    guest_permissions = [
        p for p in permissions if p.code in ["articles:read_public"] or (p.action == "read")
    ]
    guest_role.permissions = guest_permissions

    await db.commit()

    # 创建超级管理员用户（如果不存在）
    result = await db.execute(select(User).where(User.username == "admin").limit(1))
    if not result.scalar_one_or_none():
        admin_user = User(
            username="admin",
            password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "password"
            nickname="超级管理员",
            email="<EMAIL>",
            role_id=admin_role.id,
            is_active=True,
            is_superuser=True,
        )
        db.add(admin_user)
        await db.commit()
