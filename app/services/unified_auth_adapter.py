"""统一认证适配器

用于将SMS认证和微信认证的结果转换为统一格式
"""

from datetime import datetime
from typing import Any

# 移除循环导入，改为在需要时动态导入
from app.models.user import User
from app.schemas.unified_auth import (
    UnifiedAuthResponse,
    UnifiedAuthResult,
    UnifiedDeviceVerificationResponse,
    UnifiedUserInfo,
)
from app.schemas.wechat import WeChatBindResponse
from app.services.logger import get_logger
from app.services.sms_auth_service import AuthenticationResult

logger = get_logger(__name__)


class UnifiedAuthAdapter:
    """统一认证适配器类"""

    @staticmethod
    def from_sms_auth_result(result: AuthenticationResult) -> UnifiedAuthResult:
        """从SMS认证结果转换为统一认证结果"""
        # 处理User模型转换
        user_data = result.user
        if isinstance(result.user, User):
            user_data = {
                "id": result.user.id,
                "username": result.user.username,
                "nickname": result.user.nickname,
                "email": getattr(result.user, "email", None),
                "avatar": getattr(result.user, "avatar", None),
                "is_active": result.user.is_active,
                "last_login": getattr(result.user, "last_login", None),
                "created_at": getattr(result.user, "created_at", datetime.utcnow()),
                "wechat_nickname": getattr(result.user, "wechat_nickname", None),
                "wechat_avatar": getattr(result.user, "wechat_avatar", None),
                "login_type": "sms",
            }

        return UnifiedAuthResult(
            success=result.success,
            access_token=result.access_token,
            user=user_data,
            requires_device_verification=result.requires_device_verification,
            verification_token=result.verification_token,
            device_info=result.device_info,
            message=result.message,
            auth_method="sms",
        )

    @staticmethod
    def from_sms_auth_response(response: Any) -> UnifiedAuthResponse:
        """从SMS认证响应转换为统一认证响应"""
        return UnifiedAuthResponse(
            access_token=response.access_token,
            token_type=response.token_type,
            user=UnifiedUserInfo.model_validate(response.user),
            message="登录成功",
            auth_method="sms",
        )

    @staticmethod
    def from_sms_device_verification_response(
        response: Any,
    ) -> UnifiedDeviceVerificationResponse:
        """从SMS设备验证响应转换为统一设备验证响应"""
        return UnifiedDeviceVerificationResponse(
            requires_device_verification=response.requires_device_verification,
            message=response.message,
            device_info=response.device_info,
            verification_token=response.verification_token,
            auth_method="sms",
        )

    @staticmethod
    def to_legacy_sms_response(unified_response: UnifiedAuthResponse) -> Any:
        """将统一认证响应转换为SMS认证响应（向后兼容）"""
        from app.api.endpoints.sms_auth import AuthResponse, UserInfo

        user_info = UserInfo(
            id=unified_response.user.id,
            username=unified_response.user.username,
            nickname=unified_response.user.nickname,
            email=unified_response.user.email,
            avatar=unified_response.user.avatar,
            is_active=unified_response.user.is_active,
            last_login=unified_response.user.last_login,
            created_at=unified_response.user.created_at,
        )

        return AuthResponse(
            access_token=unified_response.access_token,
            token_type=unified_response.token_type,
            user=user_info,
        )

    @staticmethod
    def to_legacy_wechat_response(
        unified_response: UnifiedAuthResponse,
    ) -> WeChatBindResponse:
        """将统一认证响应转换为微信绑定响应（向后兼容）"""
        from app.schemas.wechat import UserInfo as WeChatUserInfo

        user_info = WeChatUserInfo(
            id=unified_response.user.id,
            username=unified_response.user.username,
            nickname=unified_response.user.nickname,
            wechat_nickname=unified_response.user.wechat_nickname,
            avatar=unified_response.user.wechat_avatar or unified_response.user.avatar,
        )

        return WeChatBindResponse(
            message=unified_response.message or "操作成功",
            user=user_info,
            access_token=unified_response.access_token,
            token_type=unified_response.token_type,
        )

    @staticmethod
    def create_unified_result(
        success: bool,
        access_token: str = None,
        user: Any = None,
        requires_device_verification: bool = False,
        verification_token: str = None,
        device_info: dict = None,
        message: str = None,
        auth_method: str = "unknown",
    ) -> UnifiedAuthResult:
        """创建统一认证结果"""
        return UnifiedAuthResult(
            success=success,
            access_token=access_token,
            user=user,
            requires_device_verification=requires_device_verification,
            verification_token=verification_token,
            device_info=device_info,
            message=message,
            auth_method=auth_method,
        )


# 全局适配器实例
unified_auth_adapter = UnifiedAuthAdapter()
