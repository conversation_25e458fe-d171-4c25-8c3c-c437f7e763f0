"""
文章权限服务类

专门处理文章权限逻辑的服务类，包含各种权限检查方法
"""

from fastapi import HTTPException, status
from sqlalchemy import and_, or_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app import models
from app.core.permissions import (
    ArticlePermission,
    ArticleStatus,
    UserRole,
    get_article_status,
    get_user_role_from_user,
    has_permission,
)


class ArticlePermissionService:
    """文章权限服务类"""

    @staticmethod
    async def get_accessible_articles_by_category(
        db: AsyncSession,
        category_id: int,
        current_user: models.User | None = None,
        skip: int = 0,
        limit: int = 100,
        show_draft: bool = False,
        show_pending: bool = False,
    ):
        """
        获取用户可访问的文章列表

        Args:
            db: 数据库会话
            category_id: 分类ID
            current_user: 当前用户（可为None，表示游客）
            skip: 跳过的记录数
            limit: 返回的最大记录数
            show_draft: 是否显示草稿
            show_pending: 是否显示待审核

        Returns:
            文章列表和总数
        """
        user_role = get_user_role_from_user(current_user)

        # 基础查询
        query = (
            select(models.Article)
            .where(models.Article.category_id == category_id)
            .options(
                selectinload(models.Article.tags),
                selectinload(models.Article.author),
            )
        )

        conditions = []

        if user_role == UserRole.ADMIN:
            # 管理员可以看到所有文章
            pass  # 不添加任何条件
        elif user_role == UserRole.USER:
            # 普通用户的权限逻辑
            # 1. 已发布且已审核的文章（公开文章）
            public_condition = and_(
                models.Article.is_published == True, models.Article.is_approved == True
            )
            conditions.append(public_condition)

            # 2. 自己的文章（包括草稿和待审核）
            if current_user:
                own_articles_condition = models.Article.author_id == current_user.id
                conditions.append(own_articles_condition)

            # 3. 根据参数决定是否包含特定状态的文章
            if show_draft and current_user:
                # 显示自己的草稿
                draft_condition = and_(
                    models.Article.is_published == False,
                    models.Article.author_id == current_user.id,
                )
                conditions.append(draft_condition)

            if show_pending and current_user:
                # 显示自己的待审核文章
                pending_condition = and_(
                    models.Article.is_published == True,
                    models.Article.is_approved == False,
                    models.Article.author_id == current_user.id,
                )
                conditions.append(pending_condition)

        else:  # UserRole.GUEST
            # 游客只能看到已发布且已审核的文章
            public_condition = and_(
                models.Article.is_published == True, models.Article.is_approved == True
            )
            conditions.append(public_condition)

        # 应用条件
        if conditions:
            if len(conditions) == 1:
                query = query.where(conditions[0])
            else:
                query = query.where(or_(*conditions))

        # 获取总数
        count_query = select(models.Article).where(models.Article.category_id == category_id)
        if conditions:
            if len(conditions) == 1:
                count_query = count_query.where(conditions[0])
            else:
                count_query = count_query.where(or_(*conditions))

        from sqlalchemy import func
        count_query = count_query.with_only_columns(func.count().label("count"))
        total_result = await db.execute(count_query)
        total = total_result.scalar() or 0

        # 应用分页
        query = query.offset(skip).limit(limit)

        # 执行查询
        result = await db.execute(query)
        articles = result.scalars().all()

        return articles, total

    @staticmethod
    async def check_article_access(
        db: AsyncSession, content: models.Article, current_user: models.User | None = None
    ) -> models.Article:
        """
        检查文章访问权限

        Args:
            db: 数据库会话
            article_id: 文章ID
            current_user: 当前用户

        Returns:
            文章对象

        Raises:
            HTTPException: 文章不存在或无权限访问
        """
        # 获取文章

        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文章不存在",
            )

        # 检查访问权限
        user_role = get_user_role_from_user(current_user)
        article_status = get_article_status(content)

        # 管理员可以访问所有文章
        if user_role == UserRole.ADMIN:
            return content

        # 作者可以访问自己的文章
        if current_user and content.author_id == current_user.id:
            return content

        # 其他用户只能访问已发布且已审核的文章
        if article_status == ArticleStatus.APPROVED:
            return content

        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限查看该文章",
        )

    @staticmethod
    async def check_article_update_permission(
        db: AsyncSession, content: models.Article, current_user: models.User
    ) -> models.Article:
        """
        检查文章更新权限

        Args:
            db: 数据库会话
            article_id: 文章ID
            current_user: 当前用户

        Returns:
            文章对象

        Raises:
            HTTPException: 文章不存在或无权限更新
        """
        # 获取文章
        user_role = get_user_role_from_user(current_user)

        # 管理员可以更新所有文章
        if user_role == UserRole.ADMIN:
            return content

        # 作者可以更新自己的文章
        if content.author_id == current_user.id:
            return content

        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限更新该文章",
        )

    @staticmethod
    async def check_article_delete_permission(
        db: AsyncSession, content: models.Article, current_user: models.User
    ) -> models.Article:
        """
        检查文章删除权限

        Args:
            db: 数据库会话
            article_id: 文章ID
            current_user: 当前用户

        Returns:
            文章对象

        Raises:
            HTTPException: 文章不存在或无权限删除
        """
        # 获取文章

        user_role = get_user_role_from_user(current_user)

        # 管理员可以删除所有文章
        if user_role == UserRole.ADMIN:
            return content

        # 作者可以删除自己的文章
        if content.author_id == current_user.id:
            return content

        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="没有权限删除该文章",
        )

    @staticmethod
    def can_create_article(current_user: models.User | None) -> bool:
        """
        检查用户是否可以创建文章

        Args:
            current_user: 当前用户

        Returns:
            是否可以创建文章
        """
        if not current_user:
            return False

        return has_permission(current_user, ArticlePermission.CREATE)

    @staticmethod
    def can_publish_article(current_user: models.User, article: models.Article) -> bool:
        """
        检查用户是否可以发布文章

        Args:
            current_user: 当前用户
            article: 文章对象

        Returns:
            是否可以发布文章
        """
        user_role = get_user_role_from_user(current_user)

        # 管理员可以发布所有文章
        if user_role == UserRole.ADMIN:
            return has_permission(current_user, ArticlePermission.PUBLISH_ALL)

        # 作者可以发布自己的文章
        if article.author_id == current_user.id:
            return has_permission(current_user, ArticlePermission.PUBLISH_OWN)

        return False

    @staticmethod
    def can_approve_article(current_user: models.User) -> bool:
        """
        检查用户是否可以审核文章

        Args:
            current_user: 当前用户

        Returns:
            是否可以审核文章
        """
        return has_permission(current_user, ArticlePermission.APPROVE)


# 创建服务实例
article_permission_service = ArticlePermissionService()
