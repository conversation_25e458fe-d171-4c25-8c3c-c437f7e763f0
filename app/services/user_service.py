"""用户服务模块"""

import random
from datetime import datetime

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.endpoints.auth import get_password_hash
from app.models.user import User, UserRole


async def get_random_nickname() -> str:
    """生成随机昵称"""
    adjectives = ["快乐的", "勇敢的", "聪明的", "安静的"]
    nouns = ["小猫", "狮子", "土豆", "西瓜"]
    random_number = random.randint(1, 1000)
    return f"{random.choice(adjectives)}{random.choice(nouns)}{random_number}"


class UserService:
    """用户服务类"""

    @staticmethod
    async def get_user_by_username(db: AsyncSession, username: str) -> User | None:
        """根据用户名获取用户

        Args:
            db: 数据库会话
            username: 用户名

        Returns:
            User: 用户对象，如果不存在则返回None
        """
        result = await db.execute(select(User).filter(User.username == username))
        return result.scalar_one_or_none()

    @staticmethod
    async def get_user_by_id(db: AsyncSession, user_id: int) -> User | None:
        """根据用户ID获取用户

        Args:
            db: 数据库会话
            user_id: 用户ID

        Returns:
            User: 用户对象，如果不存在则返回None
        """
        result = await db.execute(select(User).filter(User.id == user_id))
        return result.scalar_one_or_none()

    @staticmethod
    async def create_user_by_phone(
        db: AsyncSession,
        phone: str,
        nickname: str | None = None,
        password: str | None = None,
    ) -> User:
        """通过手机号创建用户

        Args:
            db: 数据库会话
            phone: 手机号
            nickname: 昵称，如果不提供则使用用户名
            password: 密码，可选

        Returns:
            User: 创建的用户对象
        """

        # 如果没有提供昵称，使用随机昵称
        if not nickname:
            nickname = await get_random_nickname()

        # 获取默认角色
        result = await db.execute(select(UserRole).filter(UserRole.is_default))
        default_role = result.scalar_one_or_none()
        role_id = default_role.id if default_role else None

        # 创建用户对象
        user = User(
            username=phone,
            nickname=nickname,
            password=get_password_hash(password) if password else None,
            role_id=role_id,
            is_active=True,
            login_type="phone",  # 设置登录方式为手机号
        )

        # 保存到数据库
        db.add(user)
        await db.commit()
        await db.refresh(user)

        return user

    @staticmethod
    async def update_last_login(db: AsyncSession, user: User) -> User:
        """更新用户最后登录时间

        Args:
            db: 数据库会话
            user: 用户对象

        Returns:
            User: 更新后的用户对象
        """
        user.last_login = datetime.utcnow()
        await db.commit()
        await db.refresh(user)
        return user

    @staticmethod
    async def is_phone_registered(db: AsyncSession, phone: str) -> bool:
        """检查手机号是否已注册

        Args:
            db: 数据库会话
            phone: 手机号

        Returns:
            bool: 如果已注册返回True，否则返回False
        """
        result = await db.execute(select(User).filter(User.username == phone))
        user = result.scalar_one_or_none()
        return user is not None
