"""分片上传服务"""

import oss2
from oss2 import SizedFileAdapter, determine_part_size
from oss2.credentials import EnvironmentVariableCredentialsProvider

from app.core.config import get_settings
from app.services.logger import get_logger

logger = get_logger(__name__)
settings = get_settings()


class PartialUpload:
    """分片上传服务，支持大文件分片上传到阿里云OSS"""

    def __init__(self):
        self.auth = oss2.ProviderAuthV4(EnvironmentVariableCredentialsProvider())
        self.bucket = oss2.Bucket(
            self.auth, settings.OSS_ENDPOINT, settings.OSS_BUCKET_NAME, region=settings.OSS_REGION
        )

    def upload(
        self,
        file_data: bytes,
        file_hash: str,
        file_type: str = "image",
        retry_times: int = 3,
    ) -> str | None:
        """上传文件到OSS

        Args:
            file_data: 文件二进制数据
            file_hash: 文件哈希值
            file_type: 文件类型，可选值：image, video，默认image
            retry_times: 重试次数，默认3次

        Returns:
            str: 上传成功返回文件URL，失败返回None
        """
        # 根据文件类型设置存储路径和扩展名
        file_type_config = {
            "image": {"path": "steam/images", "ext": ".webp"},
            "video": {"path": "steam/videos", "ext": ".webm"},
        }
        config = file_type_config.get(file_type, file_type_config["image"])
        object_name = f"{config['path']}/{file_hash}{config['ext']}"

        # 小文件直接上传
        if len(file_data) < settings.OSS_MULTIPART_THRESHOLD:
            return self._upload_small_file(file_data, object_name, retry_times)

        # 大文件分片上传
        return self._upload_large_file(file_data, object_name, retry_times)

    def _upload_small_file(
        self, file_data: bytes, object_name: str, retry_times: int
    ) -> str | None:
        """小文件直接上传

        Args:
            file_data: 文件二进制数据
            object_name: OSS对象名称
            retry_times: 重试次数

        Returns:
            str: 上传成功返回文件URL，失败返回None
        """
        for i in range(retry_times):
            try:
                self.bucket.put_object(object_name, file_data)
                return f"/{object_name}"
            except Exception as e:
                logger.error(f"Failed to upload small file: {e}, retry {i + 1}/{retry_times}")
                if i == retry_times - 1:
                    return None

    def _upload_large_file(
        self, file_data: bytes, object_name: str, retry_times: int
    ) -> str | None:
        """大文件分片上传

        Args:
            file_data: 文件二进制数据
            object_name: OSS对象名称
            retry_times: 重试次数

        Returns:
            str: 上传成功返回文件URL，失败返回None
        """
        for i in range(retry_times):
            try:
                # 初始化分片上传
                upload_id = self.bucket.init_multipart_upload(object_name).upload_id
                parts = []

                # 计算分片大小和数量
                total_size = len(file_data)
                part_size = determine_part_size(total_size, preferred_size=1 * 1024 * 1024)
                part_count = (total_size - 1) // part_size + 1

                # 使用 SizedFileAdapter 进行分片上传
                sized_file = SizedFileAdapter(file_data)
                for j in range(part_count):
                    result = self.bucket.upload_part(object_name, upload_id, j + 1, sized_file)
                    parts.append(oss2.models.PartInfo(j + 1, result.etag))

                # 完成分片上传
                self.bucket.complete_multipart_upload(object_name, upload_id, parts)
                return f"/{object_name}"

            except Exception as e:
                logger.error(f"Failed to upload large file: {e}, retry {i + 1}/{retry_times}")
                if i == retry_times - 1:
                    return None
                # 取消分片上传
                try:
                    self.bucket.abort_multipart_upload(object_name, upload_id)
                except Exception as abort_e:
                    logger.error(f"Failed to abort multipart upload: {abort_e}")
