from datetime import datetime, timed<PERSON><PERSON>
from typing import Any

from sqlalchemy import desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.user_behavior import (
    ContentSimilarity,
    RecommendationLog,
    UserBrowseHistory,
    UserInteraction,
    UserProfile,
)
from app.schemas.recommendation import (
    ContentSimilarityCreate,
    RecommendationLogCreate,
    UserBrowseHistoryCreate,
    UserInteractionCreate,
    UserProfileCreate,
    UserProfileUpdate,
)


class CRUDUserBrowseHistory(
    CRUDBase[UserBrowseHistory, UserBrowseHistoryCreate, UserBrowseHistoryCreate]
):
    """用户浏览历史CRUD操作"""

    async def create_with_user(
        self, db: AsyncSession, *, obj_in: UserBrowseHistoryCreate, user_id: int
    ) -> UserBrowseHistory:
        """创建用户浏览历史记录"""
        obj_in_data = obj_in.dict()
        obj_in_data["user_id"] = user_id
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_user_history(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        content_type: str | None = None,
        skip: int = 0,
        limit: int = 100,
        days: int = 30,
    ) -> list[UserBrowseHistory]:
        """获取用户浏览历史"""
        query = (
            select(self.model)
            .filter(
                self.model.user_id == user_id,
                self.model.created_at >= datetime.utcnow() - timedelta(days=days),
            )
        )

        if content_type:
            query = query.filter(self.model.content_type == content_type)

        query = query.order_by(desc(self.model.created_at)).offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()

    async def get_popular_content(
        self,
        db: AsyncSession,
        *,
        content_type: str | None = None,
        limit: int = 10,
        days: int = 7,
    ) -> list[tuple]:
        """获取热门浏览内容"""
        query = (
            select(
                self.model.content_type,
                self.model.content_id,
                func.count(self.model.id).label("view_count"),
                func.count(func.distinct(self.model.user_id)).label("unique_users"),
            )
            .filter(self.model.created_at >= datetime.utcnow() - timedelta(days=days))
            .group_by(self.model.content_type, self.model.content_id)
        )

        if content_type:
            query = query.filter(self.model.content_type == content_type)

        query = query.order_by(desc("view_count")).limit(limit)
        result = await db.execute(query)
        return result.all()


class CRUDUserInteraction(CRUDBase[UserInteraction, UserInteractionCreate, UserInteractionCreate]):
    """用户交互行为CRUD操作"""

    async def create_with_user(
        self, db: AsyncSession, *, obj_in: UserInteractionCreate, user_id: int
    ) -> UserInteraction:
        """创建用户交互记录"""
        obj_in_data = obj_in.dict()
        obj_in_data["user_id"] = user_id
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_user_interactions(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        interaction_type: str | None = None,
        content_type: str | None = None,
        skip: int = 0,
        limit: int = 100,
        days: int = 30,
    ) -> list[UserInteraction]:
        """获取用户交互记录"""
        query = (
            select(self.model)
            .filter(
                self.model.user_id == user_id,
                self.model.created_at >= datetime.utcnow() - timedelta(days=days),
            )
        )

        if interaction_type:
            query = query.filter(self.model.interaction_type == interaction_type)

        if content_type:
            query = query.filter(self.model.content_type == content_type)

        query = query.order_by(desc(self.model.created_at)).offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()

    async def get_user_interest_score(
        self, db: AsyncSession, *, user_id: int, content_type: str, content_id: int
    ) -> float:
        """计算用户对特定内容的兴趣分数"""
        query = (
            select(self.model)
            .filter(
                self.model.user_id == user_id,
                self.model.content_type == content_type,
                self.model.content_id == content_id,
            )
        )
        result = await db.execute(query)
        interactions = result.scalars().all()

        total_score = 0.0
        for interaction in interactions:
            total_score += interaction.weight

        return total_score


class CRUDUserProfile(CRUDBase[UserProfile, UserProfileCreate, UserProfileUpdate]):
    """用户画像CRUD操作"""

    async def get_by_user_id(self, db: AsyncSession, *, user_id: int) -> UserProfile | None:
        """根据用户ID获取用户画像"""
        query = select(self.model).filter(self.model.user_id == user_id)
        result = await db.execute(query)
        return result.scalar_one_or_none()

    async def create_with_user(
        self, db: AsyncSession, *, obj_in: UserProfileCreate, user_id: int
    ) -> UserProfile:
        """创建用户画像"""
        obj_in_data = obj_in.dict()
        obj_in_data["user_id"] = user_id
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    def update_or_create(
        self, db: Session, *, user_id: int, obj_in: UserProfileUpdate
    ) -> UserProfile:
        """更新或创建用户画像"""
        existing_profile = self.get_by_user_id(db, user_id=user_id)

        if existing_profile:
            return self.update(db, db_obj=existing_profile, obj_in=obj_in)
        else:
            create_data = UserProfileCreate(**obj_in.dict(exclude_unset=True))
            return self.create_with_user(db, obj_in=create_data, user_id=user_id)


class CRUDRecommendationLog(
    CRUDBase[RecommendationLog, RecommendationLogCreate, RecommendationLogCreate]
):
    """推荐记录CRUD操作"""

    async def create_with_user(
        self, db: AsyncSession, *, obj_in: RecommendationLogCreate, user_id: int
    ) -> RecommendationLog:
        """创建推荐日志"""
        obj_in_data = obj_in.dict()
        obj_in_data["user_id"] = user_id
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def get_user_recommendations(
        self,
        db: AsyncSession,
        *,
        user_id: int,
        content_type: str | None = None,
        skip: int = 0,
        limit: int = 100,
    ) -> list[RecommendationLog]:
        """获取用户推荐记录"""
        query = select(self.model).filter(self.model.user_id == user_id)

        if content_type:
            query = query.filter(self.model.content_type == content_type)

        query = query.order_by(desc(self.model.created_at)).offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()

    async def update_click_status(
        self, db: AsyncSession, *, log_id: int, content_id: int
    ) -> RecommendationLog | None:
        """更新推荐记录的点击状态"""
        query = select(self.model).filter(self.model.id == log_id)
        result = await db.execute(query)
        log = result.scalar_one_or_none()
        if log:
            log.is_clicked = True
            log.clicked_content_id = content_id
            log.clicked_at = datetime.utcnow()
            await db.commit()
            await db.refresh(log)
        return log

    def get_click_through_rate(
        self, db: Session, *, algorithm_type: str | None = None, days: int = 7
    ) -> float:
        """计算点击率"""
        query = db.query(self.model).filter(
            self.model.created_at >= datetime.utcnow() - timedelta(days=days)
        )

        if algorithm_type:
            query = query.filter(self.model.algorithm_type == algorithm_type)

        total_recommendations = query.count()
        clicked_recommendations = query.filter(self.model.is_clicked).count()

        if total_recommendations == 0:
            return 0.0

        return clicked_recommendations / total_recommendations


class CRUDContentSimilarity(
    CRUDBase[ContentSimilarity, ContentSimilarityCreate, ContentSimilarityCreate]
):
    """内容相似度CRUD操作"""
\
    
    async def get_similar_content(
        self,
        db: AsyncSession,
        *,
        source_content_type: str,
        source_content_id: int,
        similarity_type: str | None = None,
        limit: int = 10,
    ) -> list[ContentSimilarity]:
        """获取相似内容"""
        query = select(self.model).filter(
            self.model.source_content_type == source_content_type,
            self.model.source_content_id == source_content_id,
        )

        if similarity_type:
            query = query.filter(self.model.similarity_type == similarity_type)

        query = query.order_by(desc(self.model.similarity_score)).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()

    async def batch_create_similarities(
        self, db: AsyncSession, *, similarities: list[ContentSimilarityCreate]
    ) -> list[ContentSimilarity]:
        """批量创建相似度记录"""
        db_objs = []
        for similarity in similarities:
            obj_in_data = similarity.dict()
            db_obj = self.model(**obj_in_data)
            db_objs.append(db_obj)

        db.add_all(db_objs)
        await db.commit()

        for db_obj in db_objs:
            await db.refresh(db_obj)

        return db_objs

    def update_similarity(
        self,
        db: Session,
        *,
        source_content_type: str,
        source_content_id: int,
        target_content_type: str,
        target_content_id: int,
        similarity_type: str,
        similarity_score: float,
    ) -> ContentSimilarity:
        """更新或创建相似度记录"""
        existing = (
            db.query(self.model)
            .filter(
                self.model.source_content_type == source_content_type,
                self.model.source_content_id == source_content_id,
                self.model.target_content_type == target_content_type,
                self.model.target_content_id == target_content_id,
                self.model.similarity_type == similarity_type,
            )
            .first()
        )

        if existing:
            existing.similarity_score = similarity_score
            existing.calculated_at = datetime.utcnow()
            db.commit()
            db.refresh(existing)
            return existing
        else:
            obj_in = ContentSimilarityCreate(
                source_content_type=source_content_type,
                source_content_id=source_content_id,
                target_content_type=target_content_type,
                target_content_id=target_content_id,
                similarity_score=similarity_score,
                similarity_type=similarity_type,
            )
            return self.create(db, obj_in=obj_in)


# 创建CRUD实例
user_browse_history = CRUDUserBrowseHistory(UserBrowseHistory)
user_interaction = CRUDUserInteraction(UserInteraction)
user_profile = CRUDUserProfile(UserProfile)
recommendation_log = CRUDRecommendationLog(RecommendationLog)
content_similarity = CRUDContentSimilarity(ContentSimilarity)
