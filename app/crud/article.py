from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.crud.base import CRUDBase
from app.models.article import Article
from app.schemas.article import ArticleCreate, ArticleUpdate


class CRUDArticle(CRUDBase[Article, ArticleCreate, ArticleUpdate]):
    async def get_by_title(self, db: AsyncSession, *, title: str) -> Article | None:
        """根据标题获取文章"""
        result = await db.execute(select(self.model).where(self.model.title == title))
        return result.scalar_one_or_none()

    async def get_multi_by_author(
        self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
    ) -> list[Article]:
        """获取指定作者的文章列表"""
        result = await db.execute(
            select(self.model)
            .options(
                selectinload(self.model.tags),
                selectinload(self.model.author),
            )
            .where(self.model.author_id == author_id)
            .offset(skip)
            .limit(limit)
        )

        return result.scalars().all()

    async def get_published(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> list[Article]:
        """获取已发布的文章列表"""
        result = await db.execute(
            select(self.model)
            .options(
                selectinload(self.model.tags),
            )
            .where(self.model.is_published)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def update_publish_status(
        self, db: AsyncSession, *, db_obj: Article, is_published: bool
    ) -> Article:
        """更新文章发布状态"""
        db_obj.is_published = is_published
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj


async def get_drafts(
    self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
) -> list[Article]:
    """获取指定作者的草稿列表

    Args:
        db: 数据库会话
        author_id: 作者ID
        skip: 跳过的记录数
        limit: 返回的最大记录数

    Returns:
        草稿列表
    """
    result = await db.execute(
        select(self.model)
        .where(self.model.author_id == author_id)
        .where(~self.model.is_published)
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()


async def get_pending_review(
    self, db: AsyncSession, *, author_id: int, skip: int = 0, limit: int = 100
) -> list[Article]:
    """获取指定作者的待审核文章列表

    Args:
        db: 数据库会话
        author_id: 作者ID
        skip: 跳过的记录数
        limit: 返回的最大记录数

    Returns:
        待审核文章列表
    """
    result = await db.execute(
        select(self.model)
        .where(self.model.author_id == author_id)
        .where(self.model.is_published & ~self.model.is_approved)
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()


article = CRUDArticle(Article)
