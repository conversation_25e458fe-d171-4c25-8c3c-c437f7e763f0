#!/usr/bin/env python3
"""测试微信登录响应格式"""

import json
from app.schemas.unified_auth import UnifiedAuthResponse, UnifiedUserInfo
from datetime import datetime

# 模拟创建一个 UnifiedAuthResponse 对象
user_info = UnifiedUserInfo(
    id=8,
    username="17604840253",
    nickname="快乐的土豆196",
    email=None,
    avatar=None,
    is_active=True,
    last_login=datetime.now(),
    created_at=datetime.now(),
    wechat_nickname="",
    wechat_avatar="",
    login_type="wechat"
)

auth_response = UnifiedAuthResponse(
    access_token="test_token",
    token_type="bearer",
    user=user_info,
    message="登录成功",
    auth_method="wechat"
)

# 模拟当前的响应格式
response_data = {
    "status": "confirmed",
    "access_token": auth_response.access_token,
    "token_type": auth_response.token_type,
    "user": auth_response.user.model_dump() if hasattr(auth_response.user, 'model_dump') else auth_response.user,
    "message": auth_response.message,
    "auth_method": auth_response.auth_method
}

print("当前响应格式:")
print(json.dumps(response_data, indent=2, ensure_ascii=False, default=str))

# 检查是否包含 status 字段
if "status" in response_data:
    print("\n✅ 响应包含 status 字段")
else:
    print("\n❌ 响应缺少 status 字段")

print(f"\nstatus 字段值: {response_data.get('status')}")