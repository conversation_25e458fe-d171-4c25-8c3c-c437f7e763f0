#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试微信登录响应格式
验证修复后的响应是否包含 data.status 字段
"""

import requests
import json
import time

def test_wechat_login_response():
    """测试微信登录响应格式"""
    base_url = "http://localhost:8000"
    
    print("=== 微信登录响应格式测试 ===")
    
    try:
        # 1. 创建二维码
        print("\n1. 创建微信登录二维码...")
        create_response = requests.post(f"{base_url}/api/v1/wechat/qr-login/create")
        
        if create_response.status_code == 200:
            create_data = create_response.json()
            print(f"✅ 二维码创建成功")
            print(f"响应格式: {json.dumps(create_data, indent=2, ensure_ascii=False)}")
            
            scene_str = create_data.get('data', {}).get('scene_str')
            if scene_str:
                print(f"场景字符串: {scene_str}")
                
                # 2. 检查登录状态（等待状态）
                print("\n2. 检查登录状态（等待状态）...")
                status_response = requests.get(f"{base_url}/api/v1/wechat/qr-login/status/{scene_str}")
                
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    print(f"✅ 状态检查成功")
                    print(f"响应格式: {json.dumps(status_data, indent=2, ensure_ascii=False)}")
                    
                    # 检查响应结构
                    if 'status' in status_data and status_data['status'] == 'success':
                        print("✅ 外层 status 字段存在且为 'success'")
                        
                        if 'data' in status_data:
                            print("✅ data 字段存在")
                            data = status_data['data']
                            
                            if 'status' in data:
                                print(f"✅ data.status 字段存在，值为: '{data['status']}'")
                                
                                # 模拟登录成功的响应格式
                                print("\n3. 模拟登录成功响应格式...")
                                mock_success_response = {
                                    "status": "success",
                                    "data": {
                                        "status": "confirmed",
                                        "access_token": "mock_token_123",
                                        "token_type": "bearer",
                                        "user": {
                                            "id": 8,
                                            "username": "17604840253",
                                            "nickname": "快乐的土豆196",
                                            "login_type": "wechat"
                                        },
                                        "message": "登录成功",
                                        "auth_method": "wechat"
                                    }
                                }
                                print("期望的登录成功响应格式:")
                                print(json.dumps(mock_success_response, indent=2, ensure_ascii=False))
                                
                            else:
                                print("❌ data.status 字段不存在")
                        else:
                            print("❌ data 字段不存在")
                    else:
                        print("❌ 外层 status 字段不存在或值不正确")
                        
                else:
                    print(f"❌ 状态检查失败: {status_response.status_code}")
                    print(f"错误信息: {status_response.text}")
            else:
                print("❌ 无法获取场景字符串")
        else:
            print(f"❌ 二维码创建失败: {create_response.status_code}")
            print(f"错误信息: {create_response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_wechat_login_response()