#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
认证服务测试脚本
用于验证 AuthService 的基本功能
"""

import asyncio
from datetime import timedelta

# 添加项目路径
import sys
sys.path.append('/usr/local/data/steam_Aggregation_backend')

from app.services.auth_service import auth_service


async def test_auth_service():
    """测试认证服务的基本功能"""
    print("=== 认证服务功能测试 ===")
    
    # 测试密码哈希和验证
    print("\n1. 测试密码哈希和验证")
    password = "test_password_123"
    hashed = auth_service.get_password_hash(password)
    print(f"原始密码: {password}")
    print(f"哈希后: {hashed[:50]}...")
    
    # 验证密码
    is_valid = auth_service.verify_password(password, hashed)
    print(f"密码验证结果: {is_valid}")
    
    # 验证错误密码
    is_invalid = auth_service.verify_password("wrong_password", hashed)
    print(f"错误密码验证结果: {is_invalid}")
    
    # 测试令牌创建
    print("\n2. 测试访问令牌创建")
    token_data = {"sub": "test_user"}
    expires_delta = timedelta(minutes=30)
    
    try:
        access_token = await auth_service.create_access_token(
            data=token_data, 
            expires_delta=expires_delta
        )
        print(f"访问令牌创建成功: {access_token[:50]}...")
    except Exception as e:
        print(f"访问令牌创建失败: {e}")
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    asyncio.run(test_auth_service())